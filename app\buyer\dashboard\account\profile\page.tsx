"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { User, Mail, Phone, Calendar, Check, Camera, Shield, Info, MapPin, CreditCard, Link2 } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)
  const isMobile = useIsMobile()
  const [formData, setFormData] = useState({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+62812345678",
    dateOfBirth: "1990-01-01",
    gender: "male",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Save changes logic would go here
    setIsEditing(false)
  }

  return (
    <div className={cn("space-y-6", isMobile && "space-y-4 pb-20")}>
      {/* Mobile Header */}
      {isMobile ? (
        <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-4 rounded-lg mx-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="h-16 w-16 border-2 border-white/20">
                  <AvatarImage src="/placeholder.svg" alt="Profile" />
                  <AvatarFallback className="bg-white/20 text-white text-lg font-bold">JD</AvatarFallback>
                </Avatar>
                <Button size="icon" variant="ghost" className="absolute bottom-0 right-0 h-6 w-6 rounded-full bg-white/20 text-white hover:bg-white/30">
                  <Camera className="h-3 w-3" />
                </Button>
              </div>
              <div>
                <h2 className="font-semibold text-lg">John Doe</h2>
                <p className="text-sm opacity-90"><EMAIL></p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="bg-white/20 text-white text-xs">
                    <Shield className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                  <Badge variant="secondary" className="bg-white/20 text-white text-xs">
                    Silver
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        /* Desktop Header */
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/buyer/dashboard">Back to Dashboard</Link>
            </Button>
          </div>
        </div>
      )}

      <Tabs defaultValue="profile" className={cn("space-y-6", isMobile && "space-y-4")}>
        <TabsList className={cn(
          "grid w-full grid-cols-4 lg:w-auto",
          isMobile && "mx-2 grid-cols-2 h-auto p-1"
        )}>
          <TabsTrigger value="profile" className={cn(isMobile && "text-xs py-2")}>Profile</TabsTrigger>
          <TabsTrigger value="addresses" asChild>
            <Link href="/buyer/dashboard/account/addresses" className={cn(isMobile && "text-xs py-2")}>
              {isMobile ? "Address" : "Addresses"}
            </Link>
          </TabsTrigger>
          {!isMobile && (
            <>
              <TabsTrigger value="payment" asChild>
                <Link href="/buyer/dashboard/account/payment">Payment Methods</Link>
              </TabsTrigger>
              <TabsTrigger value="security" asChild>
                <Link href="/buyer/dashboard/account/security">Security</Link>
              </TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="profile" className={cn("space-y-6", isMobile && "space-y-4")}>
          {/* Profile Picture - Hidden on mobile since it's in header */}
          {!isMobile && (
            <Card>
              <CardHeader>
                <CardTitle>Profile Picture</CardTitle>
                <CardDescription>Your profile picture will be visible to other users on the platform.</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center justify-center sm:flex-row sm:justify-start sm:gap-6">
                <div className="relative">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src="/placeholder.svg" alt="Profile" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <Button size="icon" variant="outline" className="absolute bottom-0 right-0 h-8 w-8 rounded-full">
                    <Camera className="h-4 w-4" />
                  </Button>
                </div>
                <div className="mt-4 text-center sm:mt-0 sm:text-left">
                  <h3 className="text-lg font-medium">John Doe</h3>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                  <div className="mt-2 flex flex-wrap justify-center gap-2 sm:justify-start">
                    <Badge variant="outline" className="gap-1">
                      <Shield className="h-3 w-3" />
                      Verified Account
                    </Badge>
                    <Badge variant="secondary" className="gap-1">
                      <Check className="h-3 w-3" />
                      Silver Member
                    </Badge>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="text-sm text-muted-foreground">
                  Member since {new Date("2023-01-15").toLocaleDateString("id-ID")}
                </div>
                <Button variant="outline" size="sm">
                  Remove Picture
                </Button>
              </CardFooter>
            </Card>
          )}

          {/* Personal Information */}
          <Card className={cn(isMobile && "mx-2 shadow-sm")}>
            <CardHeader className={cn(isMobile && "px-4 py-3")}>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className={cn(isMobile && "text-lg")}>Personal Information</CardTitle>
                  <CardDescription className={cn(isMobile && "text-sm")}>
                    Update your personal details and contact information.
                  </CardDescription>
                </div>
                {!isEditing && (
                  <Button
                    onClick={() => setIsEditing(true)}
                    size={isMobile ? "sm" : "default"}
                    className={cn(isMobile && "text-xs")}
                  >
                    Edit Profile
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className={cn(isMobile && "px-4 pb-4")}>
              <form onSubmit={handleSubmit}>
                <div className={cn("grid gap-6", isMobile ? "gap-4" : "sm:grid-cols-2")}>
                  <div className={cn("space-y-2", isMobile && "space-y-1")}>
                    <Label htmlFor="firstName" className={cn(isMobile && "text-sm")}>
                      <User className={cn("mr-1 inline-block h-4 w-4", isMobile && "h-3 w-3")} />
                      First Name
                    </Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      disabled={!isEditing}
                      className={cn(isMobile && "h-10 text-sm")}
                    />
                  </div>
                  <div className={cn("space-y-2", isMobile && "space-y-1")}>
                    <Label htmlFor="lastName" className={cn(isMobile && "text-sm")}>
                      <User className={cn("mr-1 inline-block h-4 w-4", isMobile && "h-3 w-3")} />
                      Last Name
                    </Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      disabled={!isEditing}
                      className={cn(isMobile && "h-10 text-sm")}
                    />
                  </div>
                  <div className={cn("space-y-2", isMobile && "space-y-1")}>
                    <Label htmlFor="email" className={cn(isMobile && "text-sm")}>
                      <Mail className={cn("mr-1 inline-block h-4 w-4", isMobile && "h-3 w-3")} />
                      Email
                    </Label>
                    <div className="relative">
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={cn(isMobile && "h-10 text-sm pr-20")}
                      />
                      <Badge
                        variant="outline"
                        className={cn(
                          "absolute right-2 top-1/2 -translate-y-1/2 bg-green-50 text-green-600",
                          isMobile && "text-xs px-1"
                        )}
                      >
                        <Check className={cn("mr-1 h-3 w-3", isMobile && "h-2 w-2")} />
                        Verified
                      </Badge>
                    </div>
                  </div>
                  <div className={cn("space-y-2", isMobile && "space-y-1")}>
                    <Label htmlFor="phone" className={cn(isMobile && "text-sm")}>
                      <Phone className={cn("mr-1 inline-block h-4 w-4", isMobile && "h-3 w-3")} />
                      Phone Number
                    </Label>
                    <div className="relative">
                      <Input
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={cn(isMobile && "h-10 text-sm pr-20")}
                      />
                      <Badge
                        variant="outline"
                        className={cn(
                          "absolute right-2 top-1/2 -translate-y-1/2 bg-green-50 text-green-600",
                          isMobile && "text-xs px-1"
                        )}
                      >
                        <Check className={cn("mr-1 h-3 w-3", isMobile && "h-2 w-2")} />
                        Verified
                      </Badge>
                    </div>
                  </div>
                  <div className={cn("space-y-2", isMobile && "space-y-1")}>
                    <Label htmlFor="dateOfBirth" className={cn(isMobile && "text-sm")}>
                      <Calendar className={cn("mr-1 inline-block h-4 w-4", isMobile && "h-3 w-3")} />
                      Date of Birth
                    </Label>
                    <Input
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={handleChange}
                      disabled={!isEditing}
                      className={cn(isMobile && "h-10 text-sm")}
                    />
                  </div>
                  <div className={cn("space-y-2", isMobile && "space-y-1")}>
                    <Label htmlFor="gender" className={cn(isMobile && "text-sm")}>
                      <Info className={cn("mr-1 inline-block h-4 w-4", isMobile && "h-3 w-3")} />
                      Gender
                    </Label>
                    <Select
                      disabled={!isEditing}
                      value={formData.gender}
                      onValueChange={(value) => handleSelectChange("gender", value)}
                    >
                      <SelectTrigger className={cn(isMobile && "h-10 text-sm")}>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {isEditing && (
                  <div className={cn(
                    "mt-6 flex gap-2",
                    isMobile ? "flex-col" : "justify-end"
                  )}>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsEditing(false)}
                      size={isMobile ? "default" : "default"}
                      className={cn(isMobile && "w-full")}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      size={isMobile ? "default" : "default"}
                      className={cn(isMobile && "w-full")}
                    >
                      Save Changes
                    </Button>
                  </div>
                )}
              </form>
            </CardContent>
          </Card>

          {/* Mobile Quick Actions */}
          {isMobile && (
            <div className="mx-2 space-y-4">
              <Card className="shadow-sm">
                <CardContent className="p-4">
                  <h3 className="text-sm font-medium text-gray-600 mb-3">Account Actions</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <Link href="/buyer/dashboard/account/addresses" className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                        <MapPin className="w-5 h-5 text-blue-600" />
                      </div>
                      <span className="text-xs text-gray-600 text-center">Addresses</span>
                    </Link>

                    <Link href="/buyer/dashboard/account/payment" className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-2">
                        <CreditCard className="w-5 h-5 text-green-600" />
                      </div>
                      <span className="text-xs text-gray-600 text-center">Payment</span>
                    </Link>

                    <Link href="/buyer/dashboard/account/security" className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mb-2">
                        <Shield className="w-5 h-5 text-red-600" />
                      </div>
                      <span className="text-xs text-gray-600 text-center">Security</span>
                    </Link>

                    <Link href="/buyer/dashboard/account/connected" className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
                        <Link2 className="w-5 h-5 text-purple-600" />
                      </div>
                      <span className="text-xs text-gray-600 text-center">Connected</span>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
