"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Copy, ExternalLink, CheckCircle, AlertCircle, Globe, Settings, Trash2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CustomDomainManagerProps {
  tenantId: string
  currentDomain?: string
  currentSubdomain?: string
  onDomainUpdate?: () => void
}

interface DomainStatus {
  domain: string
  verified: boolean
  ssl: boolean
  lastChecked: string
}

interface PlatformConfig {
  domain: string
  ip: string
  protocol: string
  subdomainPattern: string
  dnsInstructions: {
    aRecord: { name: string; value: string; type: string }
    wildcardRecord: { name: string; value: string; type: string }
    wwwRecord: { name: string; value: string; type: string }
  }
}

export function CustomDomainManager({ tenantId, currentDomain, currentSubdomain, onDomainUpdate }: CustomDomainManagerProps) {
  const [customDomain, setCustomDomain] = useState(currentDomain || '')
  const [newDomain, setNewDomain] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [domainStatus, setDomainStatus] = useState<DomainStatus | null>(null)
  const [platformConfig, setPlatformConfig] = useState<PlatformConfig | null>(null)
  const [pendingRequest, setPendingRequest] = useState<{
    domain: string
    status: string
    requestedAt: string
  } | null>(null)
  const { toast } = useToast()

  // Default subdomain using platform config and actual subdomain
  const defaultSubdomain = platformConfig && currentSubdomain
    ? `${currentSubdomain}.${platformConfig.domain}`
    : currentSubdomain
    ? `${currentSubdomain}.sellzio.com`
    : `${tenantId}.sellzio.com`

  useEffect(() => {
    fetchPlatformConfig()
    fetchPendingRequest()
  }, [])

  useEffect(() => {
    if (customDomain) {
      checkDomainStatus(customDomain)
    }
  }, [customDomain])

  const fetchPlatformConfig = async () => {
    try {
      const response = await fetch('/api/platform/config')
      const data = await response.json()

      if (data.success) {
        setPlatformConfig(data.config)
      }
    } catch (error) {
      console.error('Error fetching platform config:', error)
      // Fallback to default
      setPlatformConfig({
        domain: 'sellzio.com',
        ip: '127.0.0.1',
        protocol: 'https',
        subdomainPattern: '*.sellzio.com',
        dnsInstructions: {
          aRecord: { name: '@', value: '127.0.0.1', type: 'A' },
          wildcardRecord: { name: '*', value: '127.0.0.1', type: 'A' },
          wwwRecord: { name: 'www', value: 'sellzio.com', type: 'CNAME' }
        }
      })
    }
  }

  const fetchPendingRequest = async () => {
    try {
      const response = await fetch(`/api/tenants/custom-domain-request?tenantId=${tenantId}`)
      const data = await response.json()

      if (data.success && data.request.status === 'pending') {
        setPendingRequest({
          domain: data.request.domain,
          status: data.request.status,
          requestedAt: data.request.requestedAt
        })
      } else {
        setPendingRequest(null)
      }
    } catch (error) {
      console.error('Error fetching pending request:', error)
      setPendingRequest(null)
    }
  }

  const checkDomainStatus = async (domain: string) => {
    try {
      console.log('🔥 DOMAIN: Checking domain status for:', domain)

      // Real domain verification check
      const response = await fetch(`/api/tenants/verify-domain?domain=${encodeURIComponent(domain)}`)

      if (response.ok) {
        const data = await response.json()
        setDomainStatus({
          domain,
          verified: data.verified || false,
          ssl: data.ssl || false,
          lastChecked: new Date().toISOString()
        })
        console.log('🔥 DOMAIN: Status check result:', data)
      } else {
        // Fallback status if API fails
        setDomainStatus({
          domain,
          verified: false,
          ssl: false,
          lastChecked: new Date().toISOString()
        })
      }
    } catch (error) {
      console.error('🔥 DOMAIN: Error checking domain status:', error)
      // Fallback status on error
      setDomainStatus({
        domain,
        verified: false,
        ssl: false,
        lastChecked: new Date().toISOString()
      })
    }
  }

  const handleSetCustomDomain = async () => {
    console.log('🔥 DOMAIN: handleSetCustomDomain called with domain:', newDomain)

    if (!newDomain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive"
      })
      return
    }

    console.log('🔥 DOMAIN: Starting custom domain request submission')
    setIsLoading(true)
    try {
      // Submit domain request for admin approval using tenants table
      const response = await fetch('/api/tenants/custom-domain-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: newDomain.trim(),
          tenantId,
          reason: "Custom domain request for business use"
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setNewDomain('')
        toast({
          title: "Request Submitted",
          description: "Your custom domain request has been submitted for admin approval. You will be notified once it's processed.",
        })
        // Refresh pending request data
        await fetchPendingRequest()
        // Refresh parent component data
        onDomainUpdate?.()
      } else {
        throw new Error(data.error || 'Failed to submit custom domain request')
      }
    } catch (error: any) {
      console.error('Error submitting custom domain request:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to submit custom domain request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelPendingRequest = async () => {
    if (!pendingRequest) return

    const confirmed = window.confirm(
      `Are you sure you want to cancel the pending request for "${pendingRequest.domain}"?`
    )

    if (!confirmed) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/tenants/custom-domain-request', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setPendingRequest(null)
        toast({
          title: "Request Cancelled",
          description: "Your custom domain request has been cancelled.",
        })
        // Refresh parent component data
        onDomainUpdate?.()
      } else {
        throw new Error(data.error || 'Failed to cancel custom domain request')
      }
    } catch (error: any) {
      console.error('Error cancelling custom domain request:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to cancel custom domain request. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyDomain = async () => {
    if (!customDomain) return

    setIsVerifying(true)
    try {
      console.log('🔥 DOMAIN: Starting domain verification for:', customDomain)

      // Real domain verification
      const response = await fetch('/api/tenants/verify-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: customDomain,
          tenantId
        })
      })

      const data = await response.json()

      if (response.ok && data.verified) {
        await checkDomainStatus(customDomain)

        toast({
          title: "Domain Verified",
          description: "Your custom domain has been verified successfully",
        })
      } else {
        toast({
          title: "Verification Failed",
          description: data.message || "Failed to verify domain. Please check your DNS settings.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('🔥 DOMAIN: Error verifying domain:', error)
      toast({
        title: "Verification Failed",
        description: "Failed to verify domain. Please check your DNS settings.",
        variant: "destructive"
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleDeleteCustomDomain = async () => {
    if (!customDomain) {
      toast({
        title: "No Custom Domain",
        description: "There is no custom domain to remove.",
        variant: "destructive"
      })
      return
    }

    const confirmed = window.confirm(
      `Are you sure you want to remove the custom domain "${customDomain}"? Your store will only be accessible via the default subdomain.`
    )

    if (!confirmed) return

    setIsLoading(true)
    try {
      console.log('🔥 DOMAIN: Deleting custom domain:', customDomain)

      const response = await fetch('/api/tenants/custom-domain', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setCustomDomain('')
        setDomainStatus(null)
        toast({
          title: "Success",
          description: "Custom domain removed automatically! DNS and Vercel cleanup completed.",
        })
        // Refresh parent component data
        onDomainUpdate?.()
      } else {
        throw new Error(data.error || 'Failed to delete custom domain')
      }
    } catch (error: any) {
      console.error('🔥 DOMAIN: Error deleting custom domain:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete custom domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    })
  }

  return (
    <div className="space-y-6">


      {/* Custom Domain */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Custom Domain
          </CardTitle>
          <CardDescription>
            Use your own domain for better branding and SEO
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Pending Custom Domain Request */}
          {pendingRequest && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-amber-800">Pending Custom Domain Request</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    Your request for <strong>{pendingRequest.domain}</strong> is awaiting admin approval.
                  </p>
                  <p className="text-xs text-amber-600 mt-1">
                    Requested on: {new Date(pendingRequest.requestedAt).toLocaleDateString()}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelPendingRequest}
                  disabled={isLoading}
                  className="text-amber-700 border-amber-300 hover:bg-amber-100"
                >
                  Cancel Request
                </Button>
              </div>
            </div>
          )}

          {/* Current Custom Domain */}
          {customDomain && (
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">Current Custom Domain</Label>
                <div className="flex items-center gap-2 mt-2">
                  <Input
                    value={customDomain}
                    readOnly
                    className="flex-1 bg-gray-50"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(customDomain)}
                    title="Copy domain"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`https://${customDomain}`, '_blank')}
                    title="Visit domain"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Domain Actions */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium">Domain Actions</p>
                  <p className="text-xs text-muted-foreground">Manage your custom domain settings</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleVerifyDomain}
                    disabled={isVerifying}
                  >
                    {isVerifying ? 'Verifying...' : 'Re-verify'}
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDeleteCustomDomain}
                    disabled={isLoading}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Remove Domain
                  </Button>
                </div>
              </div>

              {/* Domain Status */}
              {domainStatus && (
                <div className="p-3 bg-white border rounded-lg">
                  <p className="text-sm font-medium mb-2">Domain Status</p>
                  <div className="flex items-center gap-3">
                    <Badge
                      variant={domainStatus.verified ? "default" : "destructive"}
                      className={domainStatus.verified ? "bg-green-50 text-green-700 border-green-200" : ""}
                    >
                      {domainStatus.verified ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          DNS Verified
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-3 w-3 mr-1" />
                          DNS Not Verified
                        </>
                      )}
                    </Badge>

                    <Badge
                      variant={domainStatus.ssl ? "default" : "secondary"}
                      className={domainStatus.ssl ? "bg-blue-50 text-blue-700 border-blue-200" : ""}
                    >
                      {domainStatus.ssl ? 'SSL Active' : 'SSL Pending'}
                    </Badge>

                    <span className="text-xs text-muted-foreground ml-auto">
                      Last checked: {new Date(domainStatus.lastChecked).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              )}

              <Separator />
            </div>
          )}

          {/* Add/Change Domain Section */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-domain" className="text-base font-medium">
                {customDomain ? 'Change to Different Domain' : 'Add Custom Domain'}
              </Label>
              <p className="text-sm text-muted-foreground mt-1">
                {pendingRequest
                  ? 'You have a pending custom domain request. Cancel it first to submit a new one.'
                  : customDomain
                  ? 'Enter a new domain to replace your current custom domain'
                  : 'Enter your own domain name to use instead of the default subdomain'
                }
              </p>
              <div className="flex gap-2 mt-3">
                <Input
                  id="new-domain"
                  placeholder="example.com"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  className="flex-1"
                  disabled={!!pendingRequest}
                />
                <Button
                  onClick={() => {
                    console.log('🔥 BUTTON: Button clicked!')
                    handleSetCustomDomain()
                  }}
                  disabled={isLoading || !newDomain.trim() || !!pendingRequest}
                  className="min-w-[120px]"
                >
                  {isLoading ? 'Submitting...' : customDomain ? 'Change Domain' : 'Add Domain'}
                </Button>
              </div>
              {customDomain && (
                <p className="text-xs text-amber-600 mt-2">
                  ⚠️ Changing your domain will require admin approval and DNS reconfiguration
                </p>
              )}
            </div>

            {/* DNS Instructions */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>DNS Configuration Required</AlertTitle>
              <AlertDescription className="mt-2">
                <p className="mb-3">To use your custom domain, configure DNS records in your domain provider (e.g., Cloudflare):</p>

                {/* DNS Records */}
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium mb-1">For main domain (example.com):</p>
                    <div className="bg-gray-50 p-3 rounded text-sm font-mono border">
                      <div className="grid grid-cols-3 gap-2">
                        <div><strong>Type:</strong> A</div>
                        <div><strong>Name:</strong> @</div>
                        <div><strong>Value:</strong> ***********</div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      ⚠️ Use A record for apex domains (example.com)
                    </p>
                  </div>

                  {/* WWW Subdomain */}
                  <div>
                    <p className="text-sm font-medium mb-1">For www subdomain (www.example.com):</p>
                    <div className="bg-gray-50 p-3 rounded text-sm font-mono border">
                      <div className="grid grid-cols-3 gap-2">
                        <div><strong>Type:</strong> CNAME</div>
                        <div><strong>Name:</strong> www</div>
                        <div><strong>Value:</strong> cname.vercel-dns.com</div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      ℹ️ Use CNAME record for subdomains (www.example.com)
                    </p>
                  </div>
                </div>

                {/* Cloudflare Specific Instructions */}
                <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                  <p className="text-sm font-medium text-blue-800 mb-2">📋 Cloudflare Setup Instructions:</p>
                  <ol className="text-xs text-blue-700 space-y-1 list-decimal list-inside">
                    <li>Login to your Cloudflare dashboard</li>
                    <li>Select your domain</li>
                    <li>Go to DNS → Records</li>
                    <li>Add the A record and CNAME record above</li>
                    <li><strong>Set Proxy status to "DNS only" (gray cloud) ☁️</strong></li>
                    <li>Click "Save" and wait for propagation</li>
                  </ol>
                  <p className="text-xs text-blue-600 mt-2">
                    ⚠️ <strong>Important:</strong> Use "DNS only" mode for Vercel compatibility
                  </p>
                </div>

                {/* Additional Notes */}
                <div className="mt-3 space-y-1">
                  <p className="text-xs text-muted-foreground">
                    ⏱️ DNS changes may take up to 24 hours to propagate globally
                  </p>
                  <p className="text-xs text-muted-foreground">
                    🔒 SSL certificates will be automatically provisioned once DNS is verified
                  </p>
                  <p className="text-xs text-muted-foreground">
                    ✅ Use the "Re-verify" button above to check your configuration
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
