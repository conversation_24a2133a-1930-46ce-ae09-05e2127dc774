"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { ChevronLeft, Star, MapPin, Truck, Shield, Heart, Share2, ShoppingCart, Minus, Plus, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { SellzioHeader } from "@/components/themes/sellzio/sellzio-header"
import { useCart } from "@/hooks/use-cart"
import { sampleProducts, type Product } from "@/components/data/products"
import { getRelatedFlashSaleProducts } from "@/utils/product-helpers"
import {
  SellzioMallBadge,
  SellzioStarBadge,
  SellzioStarLiteBadge,
  SellzioCodBadge,
  SellzioDiscountCornerBadge,
  SellzioLiveCornerBadge,
  SellzioTerlarisBadge,
  SellzioTermurahDi<PERSON><PERSON>Badge,
  <PERSON>ll<PERSON>KomisiXtraBadge,
  SellzioBadgeStyles,
} from "@/components/themes/sellzio/product-card/sellzio-badges"

interface FlashSaleProductDetailProps {
  productId: string
}

export function FlashSaleProductDetail({ productId }: FlashSaleProductDetailProps) {
  const router = useRouter()
  const { addToCart } = useCart()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [quantity, setQuantity] = useState(1)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  // Flash sale specific data
  const [flashSaleData] = useState({
    endTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    stockSold: Math.floor(Math.random() * 80) + 10, // 10-90 sold
    totalStock: 100,
    originalPrice: "",
    flashPrice: "",
    discount: ""
  })

  useEffect(() => {
    // Fetch product data
    const foundProduct = sampleProducts.find(p => p.id === parseInt(productId))
    if (foundProduct) {
      setProduct(foundProduct)
      // Set flash sale specific prices
      flashSaleData.originalPrice = foundProduct.originalPrice || foundProduct.price
      flashSaleData.flashPrice = foundProduct.price
      flashSaleData.discount = foundProduct.discount || "50%"
    }
    setLoading(false)
  }, [productId])

  // Countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = flashSaleData.endTime.getTime() - now

      if (distance > 0) {
        setTimeLeft({
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        })
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [flashSaleData.endTime])

  const handleAddToCart = () => {
    if (!product) return
    
    addToCart({
      id: product.id.toString(),
      name: product.name,
      price: parseFloat(product.price.replace(/[^\d]/g, '')),
      image: product.image
    })
    
    toast({
      title: "Berhasil ditambahkan",
      description: `${product.name} telah ditambahkan ke keranjang`,
    })
  }

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast({
      title: isWishlisted ? "Dihapus dari wishlist" : "Ditambahkan ke wishlist",
      description: `${product?.name} ${isWishlisted ? "dihapus dari" : "ditambahkan ke"} wishlist`,
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.name,
        text: `Flash Sale! ${product?.name} di Sellzio`,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link disalin",
        description: "Link produk flash sale telah disalin ke clipboard",
      })
    }
  }

  const increaseQuantity = () => setQuantity(prev => prev + 1)
  const decreaseQuantity = () => setQuantity(prev => Math.max(1, prev - 1))

  if (loading) {
    return (
      <div className="shopee-layout">
        <SellzioHeader />
        <div className="pt-16">
          <div className="shopee-container">
            <Skeleton className="h-8 w-32 mb-4" />
            <div className="shopee-product-section">
              <Skeleton className="aspect-square w-full" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-6 w-1/2" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="shopee-layout">
        <SellzioHeader />
        <div className="pt-16">
          <div className="shopee-container text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Produk Flash Sale tidak ditemukan</h1>
            <p className="text-gray-600 mb-6">Produk flash sale yang Anda cari tidak ditemukan atau telah berakhir.</p>
            <Button onClick={() => router.push('/sellzio')}>
              Kembali ke Beranda
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const rating = parseFloat(product.rating)
  const sold = parseInt(product.sold.replace(/[^\d]/g, ''))
  const progressPercentage = (flashSaleData.stockSold / flashSaleData.totalStock) * 100

  return (
    <div className="shopee-layout flash-sale-layout">
      <SellzioHeader />
      
      {/* Flash Sale Header */}
      <div className="flash-sale-header">
        <div className="shopee-container">
          <div className="flash-sale-banner">
            <div className="flash-sale-title">
              <span className="flash-sale-icon">⚡</span>
              FLASH SALE
            </div>
            <div className="flash-sale-timer">
              <span className="timer-label">Berakhir dalam:</span>
              <div className="timer-display">
                <div className="timer-unit">
                  <span className="timer-number">{String(timeLeft.hours).padStart(2, '0')}</span>
                  <span className="timer-text">Jam</span>
                </div>
                <span className="timer-separator">:</span>
                <div className="timer-unit">
                  <span className="timer-number">{String(timeLeft.minutes).padStart(2, '0')}</span>
                  <span className="timer-text">Menit</span>
                </div>
                <span className="timer-separator">:</span>
                <div className="timer-unit">
                  <span className="timer-number">{String(timeLeft.seconds).padStart(2, '0')}</span>
                  <span className="timer-text">Detik</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="shopee-breadcrumb">
        <div className="shopee-container">
          <button 
            className="shopee-back-link"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4" />
            Kembali
          </button>
          <span className="shopee-breadcrumb-separator">{'>'}</span>
          <span className="shopee-breadcrumb-category">Flash Sale</span>
          <span className="shopee-breadcrumb-separator">{'>'}</span>
          <span className="shopee-breadcrumb-subcategory">{product.category}</span>
          <span className="shopee-breadcrumb-separator">{'>'}</span>
          <span className="shopee-breadcrumb-current">{product.shortName || product.name.substring(0, 30)}...</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="shopee-main-content">
        <div className="shopee-container">
          <div className="shopee-product-section">
            {/* Left: Product Images */}
            <div className="shopee-image-section">
              <div className="shopee-main-image">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, 40vw"
                />
                
                {/* Flash Sale Badge */}
                <div className="flash-sale-image-badge">
                  <span className="flash-sale-badge-icon">⚡</span>
                  FLASH SALE
                </div>
                
                {/* Discount Badge */}
                {product.discount && <SellzioDiscountCornerBadge discount={product.discount} />}
              </div>
              
              {/* Thumbnail Gallery */}
              <div className="shopee-thumbnail-gallery">
                <div className="shopee-thumbnail active">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>

            {/* Right: Product Info */}
            <div className="shopee-product-info flash-sale-product-info">
              <SellzioBadgeStyles />

              {/* Flash Sale Progress */}
              <div className="flash-sale-progress-section">
                <div className="flash-sale-progress-header">
                  <span className="flash-sale-progress-title">⚡ FLASH SALE</span>
                  <span className="flash-sale-progress-text">Terjual {flashSaleData.stockSold}/{flashSaleData.totalStock}</span>
                </div>
                <div className="flash-sale-progress-bar">
                  <div
                    className="flash-sale-progress-fill"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>

              {/* Product Title */}
              <div className="shopee-product-header">
                <h1 className="shopee-product-title flash-sale-title">
                  {product.isMall && <SellzioMallBadge />}
                  {product.name}
                </h1>
              </div>

              {/* Rating & Stats */}
              <div className="shopee-product-stats">
                <div className="shopee-rating">
                  <div className="shopee-stars">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${star <= rating ? 'fill-orange-400 text-orange-400' : 'fill-gray-200 text-gray-200'}`}
                      />
                    ))}
                  </div>
                  <span className="shopee-rating-text">{rating}</span>
                </div>

                <div className="shopee-divider">|</div>

                <div className="shopee-reviews">
                  <span className="shopee-reviews-count">{Math.floor(sold / 10)} Ulasan</span>
                </div>

                <div className="shopee-divider">|</div>

                <div className="shopee-sold">
                  <span className="shopee-sold-count">{sold.toLocaleString()} Terjual</span>
                </div>

                {product.cod && (
                  <>
                    <div className="shopee-divider">|</div>
                    <div className="shopee-cod-badge">
                      <SellzioCodBadge />
                    </div>
                  </>
                )}
              </div>

              {/* Flash Sale Price Section */}
              <div className="flash-sale-price-section">
                <div className="flash-sale-price-container">
                  <div className="flash-sale-price-row">
                    <span className="flash-sale-label">Harga Flash Sale:</span>
                    <div className="flash-sale-price-group">
                      {flashSaleData.originalPrice && (
                        <span className="flash-sale-original-price">{flashSaleData.originalPrice}</span>
                      )}
                      <span className="flash-sale-current-price">{flashSaleData.flashPrice}</span>
                      {flashSaleData.discount && (
                        <span className="flash-sale-discount-percent">{flashSaleData.discount} OFF</span>
                      )}
                    </div>
                  </div>
                  <div className="flash-sale-savings">
                    Hemat {flashSaleData.originalPrice ?
                      `Rp${(parseFloat(flashSaleData.originalPrice.replace(/[^\d]/g, '')) - parseFloat(flashSaleData.flashPrice.replace(/[^\d]/g, ''))).toLocaleString()}`
                      : 'hingga 50%'}
                  </div>
                </div>
              </div>

              {/* Delivery Info */}
              <div className="shopee-delivery-section">
                <div className="shopee-delivery-item">
                  <span className="shopee-delivery-label">Pengiriman</span>
                  <div className="shopee-delivery-info">
                    <Truck className="h-4 w-4 text-gray-500" />
                    <span>{product.shipping}</span>
                  </div>
                </div>

                {product.cod && (
                  <div className="shopee-delivery-item">
                    <span className="shopee-delivery-label">Pembayaran</span>
                    <div className="shopee-delivery-info">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span>COD (Bayar di Tempat)</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Quantity Selection */}
              <div className="shopee-quantity-section">
                <span className="shopee-quantity-label">Kuantitas</span>
                <div className="shopee-quantity-controls">
                  <button
                    className="shopee-quantity-btn"
                    onClick={decreaseQuantity}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <input
                    type="text"
                    value={quantity}
                    readOnly
                    className="shopee-quantity-input"
                  />
                  <button
                    className="shopee-quantity-btn"
                    onClick={increaseQuantity}
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <span className="shopee-stock-info">Stok terbatas! Sisa {flashSaleData.totalStock - flashSaleData.stockSold} buah</span>
              </div>

              {/* Flash Sale Action Buttons */}
              <div className="flash-sale-action-buttons">
                <button
                  className={`shopee-wishlist-btn ${isWishlisted ? "active" : ""}`}
                  onClick={handleWishlist}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? "fill-current" : ""}`} />
                </button>

                <button
                  className="flash-sale-cart-btn"
                  onClick={handleAddToCart}
                >
                  <ShoppingCart className="h-5 w-5" />
                  Masukkan Keranjang
                </button>

                <button
                  className="flash-sale-buy-btn"
                  onClick={() => toast({ title: "Flash Sale", description: "Beli sekarang dengan harga flash sale!" })}
                >
                  ⚡ Beli Sekarang
                </button>
              </div>
            </div>
          </div>

          {/* Store Info Section */}
          <div className="shopee-store-section">
            <div className="shopee-store-info">
              <div className="shopee-store-avatar">
                <div className="shopee-store-initial">
                  {product.storeName.charAt(0)}
                </div>
              </div>

              <div className="shopee-store-details">
                <h3 className="shopee-store-name">{product.storeName}</h3>
                <div className="shopee-store-stats">
                  <span className="shopee-store-stat">Online 2 jam lalu</span>
                  <span className="shopee-store-divider">|</span>
                  <span className="shopee-store-stat">
                    <MapPin className="h-3 w-3" />
                    {product.address.city}
                  </span>
                </div>
              </div>

              <div className="shopee-store-actions">
                <button className="shopee-store-chat">Chat</button>
                <button className="shopee-store-visit">Lihat Toko</button>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className="shopee-description-section">
            <h2 className="shopee-section-title">DESKRIPSI PRODUK FLASH SALE</h2>
            <div className="shopee-description-content">
              <div className="flash-sale-description-highlight">
                <h3>🔥 Penawaran Terbatas Flash Sale!</h3>
                <p>Dapatkan {product.name} dengan harga spesial hanya untuk hari ini! Stok terbatas dan waktu terbatas.</p>
              </div>

              <p>
                {product.name} adalah produk berkualitas tinggi yang tersedia di {product.storeName}.
                Dalam flash sale ini, Anda bisa mendapatkan produk dengan diskon hingga {flashSaleData.discount}!
                Produk ini telah terjual sebanyak {sold.toLocaleString()} unit dengan rating {rating} bintang
                dari para pembeli. Tersedia pengiriman {product.shipping.toLowerCase()} ke seluruh Indonesia.
                {product.cod && " Mendukung pembayaran COD (Cash on Delivery) untuk kemudahan berbelanja Anda."}
              </p>

              <div className="shopee-product-specs">
                <h3 className="shopee-specs-title">Spesifikasi Produk</h3>
                <div className="shopee-specs-grid">
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Kategori</span>
                    <span className="shopee-spec-value">{product.category}</span>
                  </div>
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Flash Sale</span>
                    <span className="shopee-spec-value">Ya - Diskon {flashSaleData.discount}</span>
                  </div>
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Stok Flash Sale</span>
                    <span className="shopee-spec-value">{flashSaleData.totalStock - flashSaleData.stockSold} tersisa</span>
                  </div>
                  <div className="shopee-spec-item">
                    <span className="shopee-spec-label">Asal Pengiriman</span>
                    <span className="shopee-spec-value">{product.address.city}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          <div className="shopee-reviews-section">
            <h2 className="shopee-section-title">PENILAIAN PRODUK FLASH SALE</h2>
            <div className="shopee-reviews-summary">
              <div className="shopee-rating-overview">
                <div className="shopee-rating-score">
                  <span className="shopee-rating-number">{rating}</span>
                  <span className="shopee-rating-max">dari 5</span>
                </div>
                <div className="shopee-rating-stars-large">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-5 w-5 ${star <= rating ? 'fill-orange-400 text-orange-400' : 'fill-gray-200 text-gray-200'}`}
                    />
                  ))}
                </div>
              </div>

              <div className="shopee-reviews-count">
                <span>{Math.floor(sold / 10)} ulasan flash sale</span>
              </div>
            </div>

            {/* Review List */}
            <div className="shopee-reviews-list">
              {[1, 2, 3].map((reviewIndex) => (
                <div key={reviewIndex} className="shopee-review-item">
                  <div className="shopee-review-header">
                    <div className="shopee-reviewer-avatar">
                      <span>{String.fromCharCode(65 + reviewIndex)}</span>
                    </div>
                    <div className="shopee-reviewer-info">
                      <div className="shopee-reviewer-name">FlashBuyer{reviewIndex}***</div>
                      <div className="shopee-review-rating">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-3 w-3 ${star <= rating ? 'fill-orange-400 text-orange-400' : 'fill-gray-200 text-gray-200'}`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="shopee-review-date">
                      {reviewIndex} jam lalu
                    </div>
                  </div>
                  <div className="shopee-review-content">
                    Beruntung dapat flash sale! Produk original dan harga sangat worth it.
                    {reviewIndex === 1 && "Sempat ragu tapi ternyata kualitas tetap terjaga meski harga flash sale."}
                    {reviewIndex === 2 && "Flash sale terbaik yang pernah saya ikuti, recommended!"}
                    {reviewIndex === 3 && "Pengiriman cepat meski flash sale, packaging aman dan produk sesuai ekspektasi."}
                  </div>
                </div>
              ))}

              <div className="shopee-reviews-more">
                <Button variant="outline" className="w-full">
                  Lihat Semua {Math.floor(sold / 10)} Ulasan Flash Sale
                </Button>
              </div>
            </div>
          </div>

          {/* Related Flash Sale Products */}
          <div className="shopee-related-section flash-sale-related">
            <h2 className="shopee-section-title">⚡ FLASH SALE LAINNYA</h2>
            <div className="shopee-related-products">
              {getRelatedFlashSaleProducts(product, 4)
                .map((relatedProduct) => (
                  <div
                    key={relatedProduct.id}
                    className="shopee-related-product-card flash-sale-card"
                    onClick={() => router.push(`/sellzio/products/${relatedProduct.id}`)}
                  >
                    <div className="shopee-related-product-image">
                      <Image
                        src={relatedProduct.image}
                        alt={relatedProduct.name}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 50vw, 25vw"
                      />
                      <div className="flash-sale-related-badge">
                        ⚡ FLASH SALE
                      </div>
                      {relatedProduct.discount && (
                        <div className="shopee-related-discount-badge">
                          -{relatedProduct.discount}
                        </div>
                      )}
                    </div>
                    <div className="shopee-related-product-info">
                      <h3 className="shopee-related-product-name">
                        {relatedProduct.shortName || relatedProduct.name.substring(0, 50)}...
                      </h3>
                      <div className="shopee-related-product-price">
                        <span className="shopee-related-current-price flash-sale-price">{relatedProduct.price}</span>
                        {relatedProduct.originalPrice && (
                          <span className="shopee-related-original-price">{relatedProduct.originalPrice}</span>
                        )}
                      </div>
                      <div className="shopee-related-product-stats">
                        <div className="shopee-related-rating">
                          <Star className="h-3 w-3 fill-orange-400 text-orange-400" />
                          <span>{relatedProduct.rating}</span>
                        </div>
                        <div className="shopee-related-sold">
                          {relatedProduct.sold} terjual
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
