/**
 * Utility function untuk memformat angka menjadi format yang lebih ringkas
 * Contoh: 1000 -> 1k, 1000000 -> 1jt, dll
 */
export function formatCompactNumber(num: number): string {
  if (num < 1000) {
    return num.toString()
  }
  
  if (num < 1000000) {
    const thousands = num / 1000
    return thousands % 1 === 0 
      ? `${thousands}k` 
      : `${thousands.toFixed(1)}k`
  }
  
  if (num < 1000000000) {
    const millions = num / 1000000
    return millions % 1 === 0 
      ? `${millions}jt` 
      : `${millions.toFixed(1)}jt`
  }
  
  const billions = num / 1000000000
  return billions % 1 === 0 
    ? `${billions}M` 
    : `${billions.toFixed(1)}M`
}

/**
 * Format angka dengan pemisah ribuan untuk tampilan yang lebih mudah dibaca
 */
export function formatNumberWithSeparator(num: number): string {
  return num.toLocaleString('id-ID')
}

/**
 * Format mata uang Rupiah dengan format ringkas
 */
export function formatCompactCurrency(amount: number): string {
  const formatted = formatCompactNumber(amount)
  return `Rp ${formatted}`
}

/**
 * Format angka ringkas tanpa prefix mata uang untuk label sumbu Y
 */
export function formatCompactNumberOnly(amount: number): string {
  return formatCompactNumber(amount)
}