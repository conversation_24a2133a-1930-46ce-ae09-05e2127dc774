"use client"

import Link from "next/link"
import { DollarSign, Award, FileText, Download } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface WalletCardsProps {
  className?: string
}

export function WalletCards({ className }: WalletCardsProps) {
  const walletItems = [
    {
      title: "SellzioPay",
      subtitle: "Rp1.164",
      icon: DollarSign,
      href: "/buyer/dashboard/wallet",
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Ko<PERSON>zio",
      subtitle: "Gratis 25RB!",
      icon: Award,
      href: "/buyer/dashboard/coins",
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Voucher Saya",
      subtitle: "50+ Voucher",
      icon: FileText,
      href: "/buyer/dashboard/vouchers",
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Instal SellzioPay",
      subtitle: "Ambil 50.000",
      icon: Download,
      href: "/buyer/dashboard/rewards",
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ]

  return (
    <div className={cn("mx-2", className)}>
      <Card className="shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-sm font-medium text-gray-600 mb-3">Dompet Saya</h3>
          <div className="grid grid-cols-4 gap-4">
            {walletItems.map((item, index) => (
              <Link 
                key={index}
                href={item.href} 
                className="flex flex-col items-center hover:bg-gray-50 rounded-lg p-2 transition-colors"
              >
                <div className={cn(
                  "w-12 h-12 rounded-lg flex items-center justify-center mb-2",
                  item.bgColor
                )}>
                  <item.icon className={cn("w-6 h-6", item.color)} />
                </div>
                <span className="text-xs text-gray-600 text-center leading-tight mb-1">
                  {item.title}
                </span>
                <span className="text-xs text-gray-500 text-center leading-tight">
                  {item.subtitle}
                </span>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default WalletCards
