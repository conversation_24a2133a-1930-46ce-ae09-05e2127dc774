"use client"

import Link from "next/link"
import { Package, Truck, Award, Clock, CheckCircle } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface OrderStatusCardsProps {
  className?: string
}

export function OrderStatusCards({ className }: OrderStatusCardsProps) {
  const orderStatuses = [
    {
      title: "Belum Bayar",
      count: 1,
      icon: Clock,
      href: "/buyer/dashboard/orders?status=pending",
      color: "text-red-600",
      bgColor: "bg-red-100",
      badgeColor: "bg-red-500",
    },
    {
      title: "Dikemas",
      count: 2,
      icon: Package,
      href: "/buyer/dashboard/orders?status=processing",
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      badgeColor: "bg-orange-500",
    },
    {
      title: "<PERSON><PERSON><PERSON>",
      count: 0,
      icon: Truck,
      href: "/buyer/dashboard/orders?status=shipped",
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      badgeColor: "bg-blue-500",
    },
    {
      title: "Beri Penilaian",
      count: 0,
      icon: Award,
      href: "/buyer/dashboard/orders?status=delivered",
      color: "text-green-600",
      bgColor: "bg-green-100",
      badgeColor: "bg-green-500",
    },
  ]

  return (
    <div className={cn("mx-2", className)}>
      <Card className="shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-sm font-medium text-gray-600 mb-3">Pesanan Saya</h3>
          <div className="grid grid-cols-4 gap-4">
            {orderStatuses.map((status, index) => (
              <Link 
                key={index}
                href={status.href} 
                className="flex flex-col items-center hover:bg-gray-50 rounded-lg p-2 transition-colors"
              >
                <div className={cn(
                  "w-12 h-12 rounded-lg flex items-center justify-center mb-2 relative",
                  status.bgColor
                )}>
                  <status.icon className={cn("w-6 h-6", status.color)} />
                  {status.count > 0 && (
                    <Badge className={cn(
                      "absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 text-xs text-white border-0 flex items-center justify-center",
                      status.badgeColor
                    )}>
                      {status.count}
                    </Badge>
                  )}
                </div>
                <span className="text-xs text-gray-600 text-center leading-tight">
                  {status.title}
                </span>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default OrderStatusCards
