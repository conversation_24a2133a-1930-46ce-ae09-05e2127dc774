"use client"

import Link from "next/link"
import { Heart, Star } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { formatCurrency, cn } from "@/lib/utils"

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  rating: number
  sold: number
  image: string
  discount?: number
  isWishlisted?: boolean
  badges?: string[]
}

interface MobileProductGridProps {
  products: Product[]
  columns?: 2 | 3
  className?: string
}

export function MobileProductGrid({ 
  products, 
  columns = 2, 
  className 
}: MobileProductGridProps) {
  return (
    <div className={cn(
      "grid gap-3",
      columns === 2 ? "grid-cols-2" : "grid-cols-3",
      className
    )}>
      {products.map((product) => (
        <Card key={product.id} className="overflow-hidden shadow-sm hover:shadow-md transition-shadow">
          <Link href={`/buyer/marketplace/products/${product.id}`}>
            <div className="relative aspect-square w-full bg-gray-100">
              {/* Product Image */}
              <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200" />
              
              {/* Discount Badge */}
              {product.discount && (
                <Badge 
                  variant="destructive" 
                  className="absolute top-2 left-2 text-xs px-1.5 py-0.5"
                >
                  -{product.discount}%
                </Badge>
              )}
              
              {/* Wishlist Button */}
              <Button
                size="icon"
                variant="ghost"
                className="absolute top-2 right-2 h-8 w-8 bg-white/80 hover:bg-white"
                onClick={(e) => {
                  e.preventDefault()
                  // Handle wishlist toggle
                }}
              >
                <Heart 
                  className={cn(
                    "h-4 w-4",
                    product.isWishlisted ? "fill-red-500 text-red-500" : "text-gray-600"
                  )} 
                />
              </Button>
            </div>
          </Link>
          
          <CardContent className="p-2">
            <Link href={`/buyer/marketplace/products/${product.id}`}>
              <h3 className="line-clamp-2 text-sm font-medium text-gray-900 mb-1">
                {product.name}
              </h3>
              
              {/* Price */}
              <div className="flex items-center gap-1 mb-2">
                <span className="text-sm font-bold text-gray-900">
                  {formatCurrency(product.price)}
                </span>
                {product.originalPrice && (
                  <span className="text-xs text-gray-500 line-through">
                    {formatCurrency(product.originalPrice)}
                  </span>
                )}
              </div>
              
              {/* Rating and Sold */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs text-gray-600">{product.rating}</span>
                </div>
                <span className="text-xs text-gray-500">{product.sold} sold</span>
              </div>
              
              {/* Badges */}
              {product.badges && product.badges.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {product.badges.slice(0, 2).map((badge, index) => (
                    <Badge 
                      key={index}
                      variant="secondary" 
                      className="text-[10px] px-1 py-0"
                    >
                      {badge}
                    </Badge>
                  ))}
                </div>
              )}
            </Link>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default MobileProductGrid
