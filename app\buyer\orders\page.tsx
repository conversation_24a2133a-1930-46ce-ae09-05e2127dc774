'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Search, MessageCircle, Package, Clock, Truck, CheckCircle, Star, MoreHorizontal } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface Order {
  id: string
  store_name: string
  store_avatar: string
  items: {
    id: string
    name: string
    image: string
    variant: string
    quantity: number
    price: number
  }[]
  total: number
  status: 'pending' | 'processing' | 'shipped' | 'completed' | 'cancelled'
  created_at: string
  shipping_info?: {
    courier: string
    tracking_number: string
    estimated_delivery: string
  }
}

const mockOrders: Order[] = [
  {
    id: 'ORD-001',
    store_name: 'Toko Elektronik Jaya',
    store_avatar: '/api/placeholder/40/40',
    items: [
      {
        id: '1',
        name: 'Smartphone Samsung Galaxy A54',
        image: '/api/placeholder/80/80',
        variant: 'Warna: Hitam, RAM: 8GB',
        quantity: 1,
        price: 4500000
      }
    ],
    total: 4500000,
    status: 'pending',
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'ORD-002',
    store_name: 'Fashion Store',
    store_avatar: '/api/placeholder/40/40',
    items: [
      {
        id: '2',
        name: 'Kaos Polos Premium',
        image: '/api/placeholder/80/80',
        variant: 'Warna: Putih, Size: L',
        quantity: 2,
        price: 150000
      }
    ],
    total: 300000,
    status: 'processing',
    created_at: '2024-01-14T15:20:00Z'
  },
  {
    id: 'ORD-003',
    store_name: 'Toko Buku Online',
    store_avatar: '/api/placeholder/40/40',
    items: [
      {
        id: '3',
        name: 'Buku Programming JavaScript',
        image: '/api/placeholder/80/80',
        variant: 'Edisi: 2024',
        quantity: 1,
        price: 250000
      }
    ],
    total: 250000,
    status: 'shipped',
    created_at: '2024-01-13T09:15:00Z',
    shipping_info: {
      courier: 'JNE Regular',
      tracking_number: 'JNE123456789',
      estimated_delivery: '2024-01-16'
    }
  }
]

const statusConfig = {
  pending: { label: 'Belum Bayar', icon: Package, color: 'bg-orange-100 text-orange-600' },
  processing: { label: 'Dikemas', icon: Clock, color: 'bg-blue-100 text-blue-600' },
  shipped: { label: 'Dikirim', icon: Truck, color: 'bg-purple-100 text-purple-600' },
  completed: { label: 'Selesai', icon: CheckCircle, color: 'bg-green-100 text-green-600' },
  cancelled: { label: 'Dibatalkan', icon: Package, color: 'bg-red-100 text-red-600' }
}

export default function OrdersPage() {
  const searchParams = useSearchParams()
  const statusFilter = searchParams.get('status') || 'all'
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredOrders, setFilteredOrders] = useState<Order[]>(mockOrders)

  useEffect(() => {
    let filtered = mockOrders

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(order => 
        order.store_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.items.some(item => item.name.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredOrders(filtered)
  }, [statusFilter, searchQuery])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const getStatusLabel = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig]
    return config ? config.label : status
  }

  const getStatusColor = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig]
    return config ? config.color : 'bg-gray-100 text-gray-600'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-10">
        <div className="flex items-center gap-4 p-4">
          <Link href="/buyer/saya">
            <ArrowLeft className="w-6 h-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold text-gray-900">Pesanan Saya</h1>
        </div>
        
        {/* Search */}
        <div className="px-4 pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              placeholder="Cari pesanan atau nama toko"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Status Tabs */}
        <div className="flex overflow-x-auto px-4 pb-2">
          <div className="flex gap-6 min-w-max">
            <Link 
              href="/buyer/orders"
              className={`pb-2 border-b-2 whitespace-nowrap ${
                statusFilter === 'all' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-600'
              }`}
            >
              Semua
            </Link>
            <Link 
              href="/buyer/orders?status=pending"
              className={`pb-2 border-b-2 whitespace-nowrap ${
                statusFilter === 'pending' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-600'
              }`}
            >
              Belum Bayar
            </Link>
            <Link 
              href="/buyer/orders?status=processing"
              className={`pb-2 border-b-2 whitespace-nowrap ${
                statusFilter === 'processing' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-600'
              }`}
            >
              Dikemas
            </Link>
            <Link 
              href="/buyer/orders?status=shipped"
              className={`pb-2 border-b-2 whitespace-nowrap ${
                statusFilter === 'shipped' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-600'
              }`}
            >
              Dikirim
            </Link>
            <Link 
              href="/buyer/orders?status=completed"
              className={`pb-2 border-b-2 whitespace-nowrap ${
                statusFilter === 'completed' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-600'
              }`}
            >
              Selesai
            </Link>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="p-4 space-y-4">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Package className="w-12 h-12 text-gray-400" />
            </div>
            <p className="text-gray-500 mb-2">Belum ada pesanan</p>
            <p className="text-sm text-gray-400">Pesanan kamu akan muncul di sini</p>
          </div>
        ) : (
          filteredOrders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg border">
              {/* Store Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <div className="flex items-center gap-3">
                  <img 
                    src={order.store_avatar} 
                    alt={order.store_name}
                    className="w-8 h-8 rounded-full"
                  />
                  <span className="font-medium text-gray-900">{order.store_name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(order.status)}>
                    {getStatusLabel(order.status)}
                  </Badge>
                  <Button variant="ghost" size="sm">
                    <MessageCircle className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Order Items */}
              <div className="p-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex gap-3 mb-4 last:mb-0">
                    <img 
                      src={item.image} 
                      alt={item.name}
                      className="w-20 h-20 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-1">{item.name}</h3>
                      <p className="text-sm text-gray-500 mb-2">{item.variant}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">x{item.quantity}</span>
                        <span className="font-medium text-gray-900">{formatPrice(item.price)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Shipping Info */}
              {order.shipping_info && (
                <div className="px-4 py-3 bg-blue-50 border-t">
                  <div className="flex items-center gap-2 text-sm">
                    <Truck className="w-4 h-4 text-blue-600" />
                    <span className="text-blue-600 font-medium">{order.shipping_info.courier}</span>
                    <span className="text-gray-500">•</span>
                    <span className="text-gray-600">{order.shipping_info.tracking_number}</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Estimasi tiba: {formatDate(order.shipping_info.estimated_delivery)}
                  </p>
                </div>
              )}

              {/* Order Footer */}
              <div className="flex items-center justify-between p-4 border-t bg-gray-50">
                <div className="text-sm text-gray-500">
                  {order.items.length} produk • Total: <span className="font-medium text-gray-900">{formatPrice(order.total)}</span>
                </div>
                <div className="flex gap-2">
                  {order.status === 'pending' && (
                    <Button size="sm" className="bg-orange-500 hover:bg-orange-600">
                      Bayar Sekarang
                    </Button>
                  )}
                  {order.status === 'shipped' && (
                    <Button size="sm" variant="outline">
                      Lacak Paket
                    </Button>
                  )}
                  {order.status === 'completed' && (
                    <Button size="sm" variant="outline">
                      Beli Lagi
                    </Button>
                  )}
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
