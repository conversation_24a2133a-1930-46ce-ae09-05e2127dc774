"use client"

import { useState, useEffect } from 'react'
import { getClient, getServerClient } from '@/lib/supabase'

interface Product {
  id: number
  name: string
  shortName?: string
  category: string
  subcategory?: string
  price: string
  originalPrice?: string
  discount?: string
  rating?: string
  sold?: string
  shipping?: string
  image: string
  isMall?: boolean
  cod?: boolean
  isTerlaris?: boolean
  isLive?: boolean
  address?: {
    province: string
    city: string
    district: string
    village: string
  }
  searchScore?: number
  matchDetails?: string[]
}

interface UseTenantProductsReturn {
  products: Product[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useTenantProducts(tenantSlug: string): UseTenantProductsReturn {
  console.log('🔥 HOOK: useTenantProducts called with tenantSlug:', tenantSlug)

  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProducts = async () => {
    console.log('🔥 HOOK: ===== FETCH PRODUCTS CALLED =====')
    console.log('🔥 HOOK: Tenant slug received:', tenantSlug)

    // Debug hook execution
    console.log('🔥 HOOK: fetchProducts called for tenant:', tenantSlug)

    try {
      setLoading(true)
      setError(null)

      console.log('🔥 HOOK: Starting to fetch products for tenant:', tenantSlug)
      console.log('🔥 HOOK: Hook is running successfully')

      // FORCE USE DATABASE - NO FALLBACK TO SAMPLE DATA
      console.log('🔥 HOOK: FORCING DATABASE USAGE - NO SAMPLE DATA FALLBACK')

      // Use regular client with RLS policies
      const supabase = getClient()
      console.log('🔥 TENANT PRODUCTS: Using Supabase client with RLS policies')

      // Test basic connection first
      const { data: testData, error: testError } = await supabase
        .from('tenants')
        .select('count')
        .limit(1)
      console.log('🔥 TENANT PRODUCTS: Connection test:', { testData, testError })

      // TEMPORARY: Hardcode tenant IDs for testing
      const tenantIdMap: { [key: string]: string } = {
        'test1': '96111d56-b82d-43e7-9926-8f0530dc6063',
        'test2': '9bb06506-e2a9-4f8d-876b-a1a10ef5f240',
        'beauty-shop': '550e8400-e29b-41d4-a716-446655440001',
        'electronic-store': '550e8400-e29b-41d4-a716-446655440002',
        'fashion-store': '550e8400-e29b-41d4-a716-446655440010'
      }

      const tenantIdToUse = tenantIdMap[tenantSlug]

      // Debug log to track hook execution
      console.log('🔥 HOOK: Fetching products for tenant ID:', tenantIdToUse)
      console.log('🔥 HOOK: Tenant slug:', tenantSlug)

      if (!tenantIdToUse) {
        console.log('🔥 TENANT PRODUCTS: Unknown tenant slug:', tenantSlug)
        const { sampleProducts } = await import('@/components/data/products')
        const tenantProducts = getTenantSpecificProducts(tenantSlug, sampleProducts)
        setProducts(tenantProducts)
        setLoading(false)
        return
      }

      console.log('🔥 TENANT PRODUCTS: Using hardcoded tenant ID:', tenantIdToUse, 'for slug:', tenantSlug)

      // Fetch products from database filtered by tenant with all needed fields
      console.log('🔥 TENANT PRODUCTS: Querying products with tenant_id:', tenantIdToUse)
      console.log('🔥 TENANT PRODUCTS: Query filters - status: active, visibility: visible, tenant_id:', tenantIdToUse)

      // Query products with store badge information
      console.log('🔥 TENANT PRODUCTS: Querying products with store badge info...')
      const { data, error: fetchError } = await supabase
        .from('products')
        .select(`
          id,
          sku,
          name,
          slug,
          description,
          short_description,
          price,
          compare_price,
          featured_image,
          images,
          rating,
          sold,
          shipping_info,
          address,
          store_id,
          store_name,
          discount_percentage,
          featured,
          status,
          visibility,
          created_at,
          updated_at,
          tenant_id,
          category_id,
          tenant_name,
          category_name,
          subcategory_name,
          stores!inner(
            id,
            name,
            badge_type,
            rating,
            status,
            plan
          )
        `)
        .eq('status', 'active')
        .eq('visibility', 'visible')
        .eq('tenant_id', tenantIdToUse as string)
        .order('created_at', { ascending: false })

      console.log('🔥 TENANT PRODUCTS: Query completed. Error:', fetchError, 'Data length:', data?.length)
      if (fetchError) {
        console.log('🔥 TENANT PRODUCTS: Full error object:', JSON.stringify(fetchError, null, 2))
      }
      if (data && data.length > 0) {
        console.log('🔥 TENANT PRODUCTS: Sample product:', {
          name: data[0].name,
          tenant_name: data[0].tenant_name,
          category_name: data[0].category_name
        })
      }

      if (fetchError) {
        console.error('🔥 TENANT PRODUCTS: Database query failed:', fetchError)
        throw fetchError
      }

      console.log('🔥 TENANT PRODUCTS: Fetched from database:', data?.length || 0, 'products for tenant:', tenantSlug)
      console.log('🔥 TENANT PRODUCTS: Raw database data:', data)

      // If we have database products, use them
      if (data && data.length > 0) {
        console.log('🔥 TENANT PRODUCTS: Got', data.length, 'products from database')

        // Get flash sale schedules for all products
        const productIds = data.map(item => item.id)
        console.log('🔥 TENANT PRODUCTS: Fetching flash sale schedules for product IDs:', productIds)

        // Use service key client for flash sale schedules to bypass RLS
        console.log('🔥 FLASH SALE QUERY: Using service key client to bypass RLS permissions')
        const { createClient } = await import('@supabase/supabase-js')
        const serviceSupabase = createClient(
          'https://uhtxhwhpobrbmcettmpt.supabase.co',
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVodHhod2hwb2JyYm1jZXR0bXB0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI0MDE4OCwiZXhwIjoyMDYxODE2MTg4fQ.yw5olRmawtEmDgfF1S65LgEXFRlVUUxW332NLVHqTaE'
        )

        const { data: flashSaleSchedules, error: flashSaleError } = await serviceSupabase
          .from('flash_sale_schedules')
          .select(`
            *,
            product_name,
            tenant_name,
            store_name
          `)
          .in('product_id', productIds)

        console.log('🔥 FLASH SALE QUERY: Removed is_active filter to get all flash sales')
        console.log('🔥 FLASH SALE QUERY: Product IDs:', productIds)

        console.log('🔥 TENANT PRODUCTS: Flash sale schedules:', flashSaleSchedules, 'Error:', flashSaleError)
        console.log('🔥 TENANT PRODUCTS: Flash sale schedules count:', flashSaleSchedules?.length || 0)
        if (flashSaleSchedules && flashSaleSchedules.length > 0) {
          console.log('🔥 TENANT PRODUCTS: Flash sale schedule sample:', flashSaleSchedules[0])
        }

        // Create a map of product_id to flash sale schedule
        const flashSaleMap = new Map()
        if (flashSaleSchedules) {
          console.log('🔥 FLASH SALE MAPPING: Creating map from schedules:', flashSaleSchedules)
          flashSaleSchedules.forEach(schedule => {
            // Ensure consistent string format for mapping
            const productIdKey = String(schedule.product_id)
            console.log('🔥 FLASH SALE MAPPING: Adding to map:', productIdKey, '→', schedule.product_name)
            flashSaleMap.set(productIdKey, schedule)
          })
          console.log('🔥 FLASH SALE MAPPING: Map size:', flashSaleMap.size)
          console.log('🔥 FLASH SALE MAPPING: Map keys:', Array.from(flashSaleMap.keys()))
        } else {
          console.log('🔥 FLASH SALE MAPPING: No flash sale schedules found')
        }

        // Transform database data to match our Product interface
        const transformedProducts: Product[] = data.map((item: any, index: number) => {
        // Calculate discount percentage if compare_price exists
        const discountPercentage = item.compare_price && item.price
          ? Math.round(((item.compare_price - item.price) / item.compare_price) * 100)
          : 0

        // Parse JSON fields safely
        let shippingInfo = {}
        let addressInfo = {}

        try {
          shippingInfo = typeof item.shipping_info === 'string' ? JSON.parse(item.shipping_info) : (item.shipping_info || {})
          addressInfo = typeof item.address === 'string' ? JSON.parse(item.address) : (item.address || {})
        } catch (e) {
          console.log('🔥 JSON PARSE ERROR for product:', item.name, e)
          shippingInfo = {}
          addressInfo = {}
        }

        // Get flash sale schedule for this product
        const productIdKey = String(item.id)
        const flashSaleSchedule = flashSaleMap.get(productIdKey)
        let flashSaleData = null

        console.log('🔥 FLASH SALE CHECK for product:', item.name, 'ID:', productIdKey, 'Schedule found:', !!flashSaleSchedule)
        if (!flashSaleSchedule) {
          console.log('🔥 FLASH SALE CHECK: Available map keys:', Array.from(flashSaleMap.keys()))
        }

        if (flashSaleSchedule) {
          const now = new Date()
          const startTime = flashSaleSchedule.start_time ? new Date(flashSaleSchedule.start_time) : null
          const endTime = new Date(flashSaleSchedule.end_time)

          flashSaleData = {
            startTime,
            endTime,
            stockSold: flashSaleSchedule.stock_sold || 0,
            totalStock: flashSaleSchedule.total_stock || 100,
            isActive: flashSaleSchedule.is_active,
            isComingSoon: startTime && now < startTime,
            isTimeExpired: now > endTime,
            isStockSoldOut: flashSaleSchedule.stock_sold >= flashSaleSchedule.total_stock
          }

          console.log('🔥 FLASH SALE DATA for', item.name, ':', {
            startTime: flashSaleData.startTime?.toISOString(),
            endTime: flashSaleData.endTime?.toISOString(),
            stockSold: flashSaleData.stockSold,
            totalStock: flashSaleData.totalStock,
            isComingSoon: flashSaleData.isComingSoon,
            isTimeExpired: flashSaleData.isTimeExpired,
            isStockSoldOut: flashSaleData.isStockSoldOut
          })
        }

        return {
            id: index + 1, // Use index + 1 for simple numeric ID
            name: item.name || 'Unnamed Product',
            shortName: item.short_description || item.name?.substring(0, 50) || 'Unnamed Product',
            category: item.category_name || 'Elektronik', // Use category name from database
            subcategory: item.subcategory_name || 'General', // Use subcategory name from database
            price: formatPrice(item.price || 0),
            originalPrice: item.compare_price ? formatPrice(item.compare_price) : undefined,
            discount: item.discount_percentage > 0 ? `${item.discount_percentage}%` : (discountPercentage > 0 ? `${discountPercentage}%` : undefined),
            rating: item.rating ? item.rating.toString() : '0',
            sold: item.sold ? `${item.sold}+` : '0+',
            shipping: (shippingInfo as any).type || 'Gratis Ongkir',
            image: item.featured_image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop',
            isMall: (item.stores as any)?.badge_type === 'mall ori',
            storeBadgeType: (item.stores as any)?.badge_type || null,
            cod: item.shipping_info?.cod || false,
            isTerlaris: item.sold > 100,
            isLive: flashSaleData ? true : false,
            flash_sale: flashSaleData ? true : false,
            flashSaleData, // Add flash sale schedule data
            address: {
              province: (addressInfo as any).province || 'DKI Jakarta',
              city: (addressInfo as any).city || 'Jakarta',
              district: (addressInfo as any).district || 'Senayan',
              village: (addressInfo as any).village || 'Senayan'
            }
          }
        })

        setProducts(transformedProducts)
        console.log('🔥 TENANT PRODUCTS: Transformed products:', transformedProducts.length)
        console.log('🔥 TENANT PRODUCTS: Final products data:', transformedProducts)

        // Debug flash sale data specifically
        const flashSaleProducts = transformedProducts.filter(p => (p as any).flashSaleData)
        console.log('🔥 TENANT PRODUCTS: Flash sale products found:', flashSaleProducts.length)
        flashSaleProducts.forEach(p => {
          console.log('🔥 FLASH SALE PRODUCT:', p.name, 'data:', (p as any).flashSaleData)
        })
      } else {
        console.log('🔥 TENANT PRODUCTS: No database products found - NOT USING SAMPLE DATA')
        setProducts([])
        console.log('🔥 TENANT PRODUCTS: Set empty products array')
      }
    } catch (err) {
      console.error('🔥 TENANT PRODUCTS: Error fetching products:', err)
      console.error('🔥 TENANT PRODUCTS: Error details:', JSON.stringify(err, null, 2))
      setError(err instanceof Error ? err.message : 'Failed to fetch products')

      // NO FALLBACK - Force fix database issues
      console.log('🔥 TENANT PRODUCTS: Database error - NOT using sample data fallback')
      setProducts([])
      console.log('🔥 TENANT PRODUCTS: Set empty products array due to error')
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchProducts()
  }

  useEffect(() => {
    console.log('🔥 HOOK: useEffect triggered with tenantSlug:', tenantSlug)
    if (tenantSlug) {
      fetchProducts()
    }
  }, [tenantSlug])

  console.log('🔥 HOOK: RETURNING DATA:', {
    productsCount: products.length,
    loading,
    error,
    hasFlashSale: products.some(p => (p as any).flashSaleData)
  })

  return {
    products,
    loading,
    error,
    refetch
  }
}

// Helper function to format price
function formatPrice(price: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price).replace('IDR', 'Rp')
}

// Helper function to get tenant-specific products
function getTenantSpecificProducts(tenantSlug: string, sampleProducts: any[]): Product[] {
  console.log('🔥 TENANT PRODUCTS: Creating tenant-specific products for:', tenantSlug)

  // Define tenant-specific product configurations
  const tenantConfigs: { [key: string]: {
    categories: string[],
    cityOverride: string,
    provinceOverride: string,
    productCount: number,
    startIndex: number
  } } = {
    'test1': {
      categories: ['Elektronik'],
      cityOverride: 'Jakarta',
      provinceOverride: 'DKI Jakarta',
      productCount: 20,
      startIndex: 0
    },
    'test2': {
      categories: ['Elektronik'],
      cityOverride: 'Bandung',
      provinceOverride: 'Jawa Barat',
      productCount: 15,
      startIndex: 5
    },
    'beauty-shop': {
      categories: ['Kecantikan', 'Fashion'],
      cityOverride: 'Surabaya',
      provinceOverride: 'Jawa Timur',
      productCount: 18,
      startIndex: 10
    },
    'electronic-store': {
      categories: ['Elektronik'],
      cityOverride: 'Medan',
      provinceOverride: 'Sumatera Utara',
      productCount: 25,
      startIndex: 15
    },
    'fashion-store': {
      categories: ['Fashion'],
      cityOverride: 'Yogyakarta',
      provinceOverride: 'DI Yogyakarta',
      productCount: 12,
      startIndex: 20
    }
  }

  const config = tenantConfigs[tenantSlug] || tenantConfigs['test1']

  // Get products for this tenant (with rotation to avoid same products)
  const tenantProducts = sampleProducts
    .slice(config.startIndex, config.startIndex + config.productCount)
    .concat(sampleProducts.slice(0, Math.max(0, config.productCount - (sampleProducts.length - config.startIndex))))
    .slice(0, config.productCount)
    .map((product, index) => ({
      ...product,
      id: index + 1, // Reset IDs to start from 1
      address: {
        province: config.provinceOverride,
        city: config.cityOverride,
        district: 'Central District',
        village: 'Central Village'
      }
    }))

  console.log('🔥 TENANT PRODUCTS: Generated', tenantProducts.length, 'products for tenant:', tenantSlug)
  console.log('🔥 TENANT PRODUCTS: City override:', config.cityOverride)

  return tenantProducts
}
