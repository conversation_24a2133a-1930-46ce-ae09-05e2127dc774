"use client"

import { useState } from "react"
import Link from "next/link"
import { Bell, ShoppingBag, Search, Menu, User, MessageCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/contexts/auth-context"
import { cn } from "@/lib/utils"

interface MobileHeaderProps {
  showProfile?: boolean
  showSearch?: boolean
  title?: string
  className?: string
}

export function MobileHeader({ 
  showProfile = true, 
  showSearch = false, 
  title,
  className 
}: MobileHeaderProps) {
  const { user } = useAuth()
  const [searchOpen, setSearchOpen] = useState(false)

  return (
    <div className={cn(
      "bg-gradient-to-r from-orange-500 to-red-500 text-white",
      className
    )}>
      <div className="flex items-center justify-between p-4">
        {/* Left Section */}
        <div className="flex items-center gap-3">
          {showProfile && (
            <>
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold text-orange-600">G</span>
                </div>
              </div>
              <div>
                <h2 className="font-semibold text-lg">{user?.name || "User"}</h2>
                <div className="flex items-center gap-2">
                  <span className="text-sm opacity-90">1 Pengikut</span>
                  <span className="text-sm opacity-90">25 Mengikuti</span>
                </div>
              </div>
            </>
          )}
          
          {title && !showProfile && (
            <h1 className="text-xl font-semibold">{title}</h1>
          )}
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {showSearch && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="text-white hover:bg-white/20"
              onClick={() => setSearchOpen(!searchOpen)}
            >
              <Search className="h-5 w-5" />
            </Button>
          )}
          
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-white hover:bg-white/20 relative"
            asChild
          >
            <Link href="/buyer/dashboard/notifications">
              <Bell className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 text-xs bg-red-500 text-white border-0">
                3
              </Badge>
            </Link>
          </Button>
          
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-white hover:bg-white/20 relative"
            asChild
          >
            <Link href="/buyer/dashboard/cart">
              <ShoppingBag className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 text-xs bg-red-500 text-white border-0">
                2
              </Badge>
            </Link>
          </Button>

          <Button 
            variant="ghost" 
            size="icon" 
            className="text-white hover:bg-white/20"
            asChild
          >
            <Link href="/buyer/dashboard/messages">
              <MessageCircle className="h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>

      {/* Search Bar (expandable) */}
      {searchOpen && (
        <div className="px-4 pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Cari produk, toko, atau kategori..."
              className="w-full pl-10 pr-4 py-2 rounded-lg bg-white/90 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/50"
              autoFocus
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default MobileHeader
