import { SellzioProductDetail, FlashSaleProductDetail } from "@/components/themes/sellzio/product-detail"
import { sampleProducts } from "@/components/data/products"
import { isFlashSaleProduct } from "@/utils/product-helpers"

export default function ProductDetailPage({
  params,
  searchParams
}: {
  params: { id: string }
  searchParams: { 'flash-sale'?: string }
}) {
  // Check if product is flash sale - berdasarkan logic yang sama dengan halaman utama
  const product = sampleProducts.find(p => p.id === parseInt(params.id))
  const isFlashSaleFromUrl = searchParams['flash-sale'] === 'true'

  if (!product) {
    return <SellzioProductDetail productId={params.id} />
  }

  // Gunakan helper function untuk konsistensi OR URL parameter
  const isFlashSale = isFlashSaleProduct(product) || isFlashSaleFromUrl

  // Render appropriate component based on product type
  if (isFlashSale) {
    return <FlashSaleProductDetail productId={params.id} />
  }

  return <SellzioProductDetail productId={params.id} />
}
