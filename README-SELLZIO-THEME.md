# 🛍️ Theme Sellzio untuk Tenant - Marketplace Modern

## 📍 Lokasi Theme
- **Path Utama**: `app/tenant/[slug]/sellzio/`
- **Komponen**: `components/themes/sellzio/`
- **URL Pattern**: `/{tenant-slug}/sellzio`

## 🎨 Fitur Theme Sellzio

### 1. Header & Navigation
- **Header Responsif** dengan search bar yang dapat diperluas
- **Animated Placeholder** dengan rotasi teks pencarian
- **Cart & Chat Icons** dengan badge notifikasi
- **Search Predictions** dengan keyword suggestions
- **Filter Panel** untuk pencarian lanjutan

### 2. Product Cards
- **Standard Card**: Layout dasar dengan rating, harga, dan shipping
- **Video Card**: Untuk produk dengan video demo
- **Flash Sale Card**: Dengan countdown timer dan progress bar
- **Badge System**: Mall, Star, Terlaris, COD, Live, Discount
- **Masonry Layout**: Grid responsif dengan berbagai ukuran

### 3. Search & Discovery
- **Smart Search**: Pencarian dengan scoring dan relevance
- **Keyword Predictions**: Berdasarkan history dan trending
- **Search Suggestions**: History pencarian dan produk populer
- **Facet Filtering**: Filter berdasarkan kategori, harga, rating

### 4. Categories & Navigation
- **Category Grid**: Tampilan kategori dengan ikon
- **Subcategory View**: Navigasi sub-kategori
- **Shopee Live Video**: Integrasi video streaming
- **Product Masonry**: Layout grid yang responsif

## 🛠️ Komponen Utama

### Layout & Wrapper
```
app/tenant/[slug]/sellzio/
├── layout.tsx              # Layout utama dengan metadata SEO
├── page.tsx                # Halaman utama marketplace
├── sellzio-tenant-wrapper.tsx  # Wrapper dengan theme provider
├── checkout/               # Halaman checkout
└── products/[id]/          # Detail produk
```

### Theme Components
```
components/themes/sellzio/
├── sellzio-header.tsx      # Header dengan search
├── sellzio-facet.tsx       # Panel filter
├── categories.tsx          # Grid kategori
├── shopee-live-video.tsx   # Video streaming
├── product-card/           # Berbagai jenis kartu produk
├── cart/                   # Modal keranjang
├── search/                 # Komponen pencarian
├── checkout/               # Proses checkout
├── payment/                # Sistem pembayaran
└── sellzio-styles.css      # CSS theme
```

## 🎯 Fitur Khusus

### 1. Responsive Design
- **Mobile First**: Optimasi untuk perangkat mobile
- **Tablet & Desktop**: Layout yang menyesuaikan ukuran layar
- **Touch Friendly**: Interaksi yang mudah di touchscreen

### 2. Performance
- **Image Optimization**: Lazy loading dan placeholder
- **CSS-in-JS**: Styling yang optimal
- **Component Splitting**: Loading yang efisien

### 3. User Experience
- **Smooth Animations**: Transisi yang halus
- **Interactive Elements**: Hover effects dan feedback
- **Accessibility**: Support untuk screen readers

### 4. E-commerce Features
- **Shopping Cart**: Keranjang belanja dengan modal
- **Product Variants**: Pilihan varian produk
- **Shipping Calculator**: Kalkulasi ongkos kirim
- **Payment Integration**: Integrasi sistem pembayaran

## 🔧 Konfigurasi Theme

### CSS Variables
```css
:root {
  --sellzio-primary: #ee4d2d;    /* Warna utama Sellzio */
  --sellzio-secondary: #ff6b35;   /* Warna sekunder */
  --sellzio-accent: #f5a623;      /* Warna aksen */
}
```

### Theme Provider
- **TenantThemeProvider**: Mengelola konfigurasi theme per tenant
- **Dynamic Styling**: CSS yang dapat disesuaikan per tenant
- **Brand Colors**: Warna yang dapat dikustomisasi

## 📱 URL Structure
- `/{tenant-slug}/sellzio` - Halaman utama marketplace
- `/{tenant-slug}/sellzio/products/{id}` - Detail produk
- `/{tenant-slug}/sellzio/checkout` - Proses checkout
- `/{tenant-slug}/sellzio/search` - Halaman pencarian

## 🚀 Cara Penggunaan

1. **Tenant mengakses**: `https://domain.com/{tenant-slug}/sellzio`
2. **Theme otomatis load**: Berdasarkan slug tenant
3. **Data produk**: Diambil dari database berdasarkan tenant_id
4. **Kustomisasi**: Theme dapat disesuaikan per tenant

## 💡 Keunggulan Theme Sellzio

- ✅ **Modern Design**: Mengikuti tren e-commerce terkini
- ✅ **Mobile Optimized**: Pengalaman mobile yang excellent
- ✅ **SEO Friendly**: Metadata dan struktur yang optimal
- ✅ **Fast Loading**: Performance yang dioptimalkan
- ✅ **User Friendly**: Interface yang intuitif
- ✅ **Customizable**: Dapat disesuaikan per tenant

## 🖼️ Preview Theme

### Header & Search
- Header dengan warna khas Sellzio (#ee4d2d)
- Search bar dengan animated placeholder
- Icons cart dan chat dengan badge notifikasi
- Mode expanded untuk pencarian

### Product Cards
- Layout masonry yang responsif
- Badge system yang lengkap (Mall, Star, Terlaris, COD, Live)
- Discount corner badge di pojok gambar
- Rating dengan bintang merah
- Shipping info dengan icon lokasi hijau
- Hover effects yang smooth

### Categories
- Grid kategori dengan icon
- Subcategory navigation
- Shopee Live video integration
- Responsive layout untuk semua device

### Search Features
- Smart search dengan scoring
- Keyword predictions real-time
- Search history dan trending keywords
- Facet filtering panel
- Search suggestions popup

## 🔄 Integrasi Database

Theme Sellzio terintegrasi dengan:
- **Supabase Database**: Untuk data produk dan tenant
- **Real-time Data**: Produk diambil berdasarkan tenant_id
- **Fallback System**: Menggunakan sample data jika database kosong
- **Error Handling**: Graceful degradation jika ada error

## �️ Real Product Images

### Image System:
- **Real Images**: Menggunakan Unsplash API untuk image produk yang benar
- **Category-based**: Image dipilih berdasarkan kategori produk
- **Responsive**: Image otomatis resize sesuai container
- **Fallback**: Graceful fallback jika image gagal load

### Image Categories:
- **Gaming**: Console, controller, gaming accessories
- **TV**: Smart TV, TV box, brackets, cables
- **Audio**: Speakers, headphones, microphones, earbuds
- **Phone**: Smartphones, tablets, landline phones
- **Appliance**: Home appliances, electronics

### Implementation:
```typescript
// Helper function untuk mendapatkan image berdasarkan kategori
function getImageByCategory(subcategory: string, index: number): string {
  // Logic untuk mapping kategori ke image yang sesuai
  // Menggunakan Unsplash API dengan parameter yang optimal
}
```

### Image URLs:
- **Format**: `https://images.unsplash.com/photo-{id}?w=400&h=400&fit=crop&crop=center`
- **Size**: 400x400px optimized untuk product cards
- **Quality**: High quality product images
- **Loading**: Lazy loading dengan placeholder

## �📊 Analytics & Tracking

- **Search Analytics**: Tracking keyword dan frequency
- **User Interaction**: History pencarian yang berhasil
- **Product Views**: Tracking produk yang dilihat
- **Cart Analytics**: Tracking item yang ditambahkan ke cart

## 🎨 Customization Options

Tenant dapat mengkustomisasi:
- **Brand Colors**: Warna utama theme
- **Logo**: Logo tenant di header
- **Product Categories**: Kategori yang ditampilkan
- **Featured Products**: Produk unggulan
- **Shipping Options**: Metode pengiriman
- **Payment Methods**: Metode pembayaran

## 📏 Visual Spacing System

### Jarak Visual Antar Komponen (Konsisten):
- **Header ke Categories**: 24px visual spacing
- **Categories ke Sellzio Live**: 24px visual spacing
- **Sellzio Live ke Products**: 24px visual spacing
- **Products ke Footer**: 24px visual spacing

### Responsive Spacing:
- **Mobile (< 768px)**: 24px spacing
- **Tablet (768px - 1024px)**: 32px spacing
- **Desktop (> 1024px)**: 40px spacing

### Implementation:
```css
.visual-spacing-24 {
  margin-bottom: 24px; /* Mobile */
}

@media (min-width: 768px) {
  .visual-spacing-24 {
    margin-bottom: 32px; /* Tablet */
  }
}

@media (min-width: 1024px) {
  .visual-spacing-24 {
    margin-bottom: 40px; /* Desktop */
  }
}
```

### Visual Spacing Benefits:
- ✅ **Konsisten**: Jarak yang sama di semua breakpoint
- ✅ **Clean**: Tidak ada margin/padding yang bertumpuk
- ✅ **Responsive**: Menyesuaikan dengan ukuran layar
- ✅ **Maintainable**: Mudah diubah dari satu tempat

## 📊 Jarak Visual Aktual (Sudah Diimplementasikan):

### Komponen ke Komponen:
1. **Header → Categories**: 24px (mobile) | 32px (tablet) | 40px (desktop)
2. **Categories → Sellzio Live**: 24px (mobile) | 32px (tablet) | 40px (desktop)
3. **Sellzio Live → Product Cards**: 24px (mobile) | 32px (tablet) | 40px (desktop)
4. **Product Cards → Footer**: 24px (mobile) | 32px (tablet) | 40px (desktop)

### Implementasi CSS:
```css
/* Base spacing untuk mobile */
.visual-spacing-24 {
  margin-bottom: 24px;
}

/* Tablet spacing */
@media (min-width: 768px) {
  .visual-spacing-24 {
    margin-bottom: 32px;
  }
}

/* Desktop spacing */
@media (min-width: 1024px) {
  .visual-spacing-24 {
    margin-bottom: 40px;
  }
}
```

### Hasil Visual:
- ✅ **Jarak konsisten** di semua breakpoint
- ✅ **Tidak ada gap yang tidak rata** antar komponen
- ✅ **Visual hierarchy** yang jelas dan rapi
- ✅ **Responsive design** yang optimal
