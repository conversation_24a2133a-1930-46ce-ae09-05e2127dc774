import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { domain, txtValue } = await request.json()

    if (!domain || !txtValue) {
      return NextResponse.json({
        success: false,
        error: 'Domain and TXT value are required'
      }, { status: 400 })
    }

    console.log('🔥 API: Adding manual TXT record for domain:', domain)

    // Add TXT record to Cloudflare
    const txtRecordResponse = await fetch(`https://api.cloudflare.com/client/v4/zones/${process.env.CLOUDFLARE_ZONE_ID}/dns_records`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'TXT',
        name: `_vercel.${domain}`,
        content: txtValue,
        proxied: false
      })
    })

    const txtRecordData = await txtRecordResponse.json()

    if (!txtRecordResponse.ok) {
      console.error('🔥 API: Cloudflare TXT record error:', txtRecordData)
      return NextResponse.json({
        success: false,
        error: 'Failed to add TXT record to Cloudflare',
        details: txtRecordData
      }, { status: 500 })
    }

    console.log('🔥 API: TXT record added successfully:', txtRecordData)

    return NextResponse.json({
      success: true,
      message: 'TXT record added successfully',
      record: {
        name: `_vercel.${domain}`,
        value: txtValue,
        id: txtRecordData.result?.id
      }
    })

  } catch (error) {
    console.error('🔥 API: Error adding TXT record:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
