"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { BuyerHeader } from "@/components/buyer/buyer-header"
import { BuyerSidebar } from "@/components/buyer/buyer-sidebar"
import { useAuth } from "@/contexts/auth-context"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarProvider } from "@/components/ui/sidebar"
import { BottomNavigation } from "@/components/buyer/mobile/bottom-navigation"
import { useIsMobile } from "@/hooks/use-mobile"

export default function SayaLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const isMobile = useIsMobile()

  useEffect(() => {
    if (!loading && !user && typeof window !== 'undefined') {
      if (!window.location.pathname.includes('/login')) {
        router.push("/login")
      }
    }
  }, [loading, user, router])

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col bg-background">
        <div className="flex h-16 items-center justify-between border-b px-6">
          <Skeleton className="h-8 w-32" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
        <div className="flex flex-1">
          <div className="w-64 border-r p-6">
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-full" />
              ))}
            </div>
          </div>
          <main className="flex-1 p-6">
            <div className="space-y-6">
              <Skeleton className="h-8 w-64" />
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-32 w-full" />
                ))}
              </div>
            </div>
          </main>
        </div>
      </div>
    )
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen flex-col bg-background">
        {/* Header hanya ditampilkan pada desktop */}
        {!isMobile && <BuyerHeader />}
        <div className="flex flex-1">
          {/* Sidebar untuk desktop */}
          {!isMobile && <BuyerSidebar />}
          <main className={`flex-1 overflow-auto ${isMobile ? 'pb-20 p-0' : 'pb-6 p-6'}`}>
            {children}
          </main>
        </div>
        {/* Bottom Navigation hanya ditampilkan pada mobile */}
        {isMobile && <BottomNavigation />}
      </div>
    </SidebarProvider>
  )
}
