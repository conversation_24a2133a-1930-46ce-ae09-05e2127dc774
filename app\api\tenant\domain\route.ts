import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { action, tenantId, domain } = await request.json()

    if (!action || !tenantId) {
      return NextResponse.json({
        success: false,
        error: 'Action and tenant ID are required'
      }, { status: 400 })
    }

    console.log('🔥 Tenant Domain API:', { action, tenantId, domain })

    if (action === 'request') {
      // Tenant requesting new custom domain
      if (!domain) {
        return NextResponse.json({
          success: false,
          error: 'Domain is required for request action'
        }, { status: 400 })
      }

      // Check if domain is already taken
      const { data: existingDomain, error: checkError } = await supabase
        .from('tenants')
        .select('id, custom_domain')
        .or(`custom_domain.eq.${domain},custom_domain_request.eq.${domain}`)
        .neq('id', tenantId)

      if (checkError) {
        console.error('🔥 Tenant Domain API: Error checking domain:', checkError)
        return NextResponse.json({
          success: false,
          error: 'Failed to check domain availability'
        }, { status: 500 })
      }

      if (existingDomain && existingDomain.length > 0) {
        return NextResponse.json({
          success: false,
          error: 'Domain is already taken or requested by another tenant'
        }, { status: 409 })
      }

      // Update tenant with domain request
      const { error: updateError } = await supabase
        .from('tenants')
        .update({
          custom_domain_request: domain,
          custom_domain_status: 'pending',
          custom_domain_requested_at: new Date().toISOString()
        })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 Tenant Domain API: Error updating tenant:', updateError)
        return NextResponse.json({
          success: false,
          error: 'Failed to submit domain request'
        }, { status: 500 })
      }

      console.log('🔥 Tenant Domain API: Domain request submitted successfully')
      return NextResponse.json({
        success: true,
        message: 'Domain request submitted successfully. Waiting for admin approval.',
        domain,
        status: 'pending'
      })

    } else if (action === 'edit') {
      // Tenant requesting to edit existing custom domain
      if (!domain) {
        return NextResponse.json({
          success: false,
          error: 'New domain is required for edit action'
        }, { status: 400 })
      }

      // Get current tenant data
      const { data: currentTenant, error: fetchError } = await supabase
        .from('tenants')
        .select('custom_domain, custom_domain_status')
        .eq('id', tenantId)
        .single()

      if (fetchError || !currentTenant) {
        return NextResponse.json({
          success: false,
          error: 'Tenant not found'
        }, { status: 404 })
      }

      if (!currentTenant.custom_domain) {
        return NextResponse.json({
          success: false,
          error: 'No custom domain to edit. Please request a domain first.'
        }, { status: 400 })
      }

      // Check if new domain is already taken
      const { data: existingDomain, error: checkError } = await supabase
        .from('tenants')
        .select('id, custom_domain')
        .or(`custom_domain.eq.${domain},custom_domain_request.eq.${domain}`)
        .neq('id', tenantId)

      if (checkError) {
        console.error('🔥 Tenant Domain API: Error checking domain:', checkError)
        return NextResponse.json({
          success: false,
          error: 'Failed to check domain availability'
        }, { status: 500 })
      }

      if (existingDomain && existingDomain.length > 0) {
        return NextResponse.json({
          success: false,
          error: 'New domain is already taken or requested by another tenant'
        }, { status: 409 })
      }

      // Update tenant with edit request
      const { error: updateError } = await supabase
        .from('tenants')
        .update({
          custom_domain_request: domain,
          custom_domain_status: 'pending',
          custom_domain_requested_at: new Date().toISOString()
        })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 Tenant Domain API: Error updating tenant:', updateError)
        return NextResponse.json({
          success: false,
          error: 'Failed to submit domain edit request'
        }, { status: 500 })
      }

      console.log('🔥 Tenant Domain API: Domain edit request submitted successfully')
      return NextResponse.json({
        success: true,
        message: `Domain edit request submitted. Changing from ${currentTenant.custom_domain} to ${domain}. Waiting for admin approval.`,
        oldDomain: currentTenant.custom_domain,
        newDomain: domain,
        status: 'pending'
      })

    } else if (action === 'delete') {
      // Tenant requesting to delete custom domain
      const { data: currentTenant, error: fetchError } = await supabase
        .from('tenants')
        .select('custom_domain')
        .eq('id', tenantId)
        .single()

      if (fetchError || !currentTenant) {
        return NextResponse.json({
          success: false,
          error: 'Tenant not found'
        }, { status: 404 })
      }

      if (!currentTenant.custom_domain) {
        return NextResponse.json({
          success: false,
          error: 'No custom domain to delete'
        }, { status: 400 })
      }

      // For delete, we can do it immediately or require admin approval
      // Let's require admin approval for consistency
      const { error: updateError } = await supabase
        .from('tenants')
        .update({
          custom_domain_request: 'DELETE_REQUEST',
          custom_domain_status: 'pending',
          custom_domain_requested_at: new Date().toISOString()
        })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 Tenant Domain API: Error updating tenant:', updateError)
        return NextResponse.json({
          success: false,
          error: 'Failed to submit domain deletion request'
        }, { status: 500 })
      }

      console.log('🔥 Tenant Domain API: Domain deletion request submitted successfully')
      return NextResponse.json({
        success: true,
        message: `Domain deletion request submitted for ${currentTenant.custom_domain}. Waiting for admin approval.`,
        domain: currentTenant.custom_domain,
        status: 'pending'
      })
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 })

  } catch (error) {
    console.error('🔥 Tenant Domain API: Unexpected error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')

    if (!tenantId) {
      return NextResponse.json({
        success: false,
        error: 'Tenant ID is required'
      }, { status: 400 })
    }

    // Get tenant domain info
    const { data: tenant, error } = await supabase
      .from('tenants')
      .select('custom_domain, custom_domain_status, custom_domain_request, custom_domain_requested_at, subdomain')
      .eq('id', tenantId)
      .single()

    if (error) {
      console.error('🔥 Tenant Domain API: Error fetching tenant:', error)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch tenant domain info'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: {
        customDomain: tenant.custom_domain,
        customDomainStatus: tenant.custom_domain_status,
        customDomainRequest: tenant.custom_domain_request,
        customDomainRequestedAt: tenant.custom_domain_requested_at,
        subdomain: tenant.subdomain
      }
    })

  } catch (error) {
    console.error('🔥 Tenant Domain API: Unexpected error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
