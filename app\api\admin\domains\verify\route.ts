import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const getClient = () => {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
}

export async function POST(request: NextRequest) {
  try {
    const { tenantId, domain, verificationType = 'dns' } = await request.json()

    if (!tenantId || !domain) {
      return NextResponse.json(
        { error: 'Tenant ID and domain are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Starting domain verification:', { tenantId, domain, verificationType })

    const supabase = getClient()

    // Generate verification token
    const verificationToken = `sellzio-verify-${Date.now()}-${Math.random().toString(36).substring(2)}`
    const verificationValue = `sellzio-domain-verification=${verificationToken}`

    // Create or update verification record
    const { data: verificationRecord, error: verificationError } = await supabase
      .from('domain_verification_records')
      .upsert({
        tenant_id: tenantId,
        domain,
        verification_type: verificationType,
        verification_method: 'txt_record',
        verification_token: verificationToken,
        verification_value: verificationValue,
        status: 'pending',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        attempts: 0
      }, {
        onConflict: 'tenant_id,domain,verification_type'
      })
      .select()
      .single()

    if (verificationError) {
      console.error('🔥 API: Error creating verification record:', verificationError)
      return NextResponse.json(
        { error: 'Failed to create verification record' },
        { status: 500 }
      )
    }

    // Update tenant domain verification status to pending
    const { error: tenantUpdateError } = await supabase
      .from('tenants')
      .update({
        domain_verification_status: 'pending',
        domain_verification_method: 'txt_record',
        domain_verification_token: verificationToken
      })
      .eq('id', tenantId)

    if (tenantUpdateError) {
      console.error('🔥 API: Error updating tenant verification status:', tenantUpdateError)
    }

    console.log('🔥 API: Domain verification initiated successfully')

    return NextResponse.json({
      success: true,
      verification: {
        id: verificationRecord.id,
        domain,
        type: verificationType,
        method: 'txt_record',
        token: verificationToken,
        value: verificationValue,
        instructions: {
          type: 'DNS TXT Record',
          name: '_sellzio-challenge',
          value: verificationValue,
          ttl: 300
        },
        expiresAt: verificationRecord.expires_at
      }
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error in domain verification:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')
    const domain = searchParams.get('domain')

    if (!tenantId || !domain) {
      return NextResponse.json(
        { error: 'Tenant ID and domain are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Checking domain verification:', { tenantId, domain })

    const supabase = getClient()

    // Simulate DNS checking (in real implementation, you'd use DNS lookup)
    const isVerified = Math.random() > 0.5 // 50% chance of verification

    if (isVerified) {
      // Update verification record
      const { error: verificationError } = await supabase
        .from('domain_verification_records')
        .update({
          status: 'verified',
          verified_at: new Date().toISOString(),
          last_check_at: new Date().toISOString()
        })
        .eq('tenant_id', tenantId)
        .eq('domain', domain)

      // Update tenant verification status
      const { error: tenantError } = await supabase
        .from('tenants')
        .update({
          domain_verification_status: 'verified',
          domain_verified_at: new Date().toISOString()
        })
        .eq('id', tenantId)

      if (verificationError || tenantError) {
        console.error('🔥 API: Error updating verification status:', { verificationError, tenantError })
      }

      console.log('🔥 API: Domain verification successful')

      return NextResponse.json({
        success: true,
        verified: true,
        message: 'Domain verification successful'
      })
    } else {
      // Update last check time
      const { error: updateError } = await supabase
        .from('domain_verification_records')
        .update({
          last_check_at: new Date().toISOString(),
          attempts: supabase.rpc('increment_attempts', { record_id: tenantId })
        })
        .eq('tenant_id', tenantId)
        .eq('domain', domain)

      console.log('🔥 API: Domain verification pending')

      return NextResponse.json({
        success: true,
        verified: false,
        message: 'Domain verification pending. Please ensure DNS records are properly configured.'
      })
    }

  } catch (error) {
    console.error('🔥 API: Unexpected error in domain verification check:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
