"use client"

import { Suspense, useEffect, useState } from "react"
import { Building, CreditCard, DollarSign, Globe, Server, Settings, ShieldCheck, Store, Users } from "lucide-react"
import { StatCard } from "@/components/dashboard/stat-card"
import { ChartCard } from "@/components/dashboard/chart-card"
import { SystemStatus } from "@/components/dashboard/system-status"
import { ActivityFeed } from "@/components/dashboard/activity-feed"
import { QuickActions } from "@/components/dashboard/quick-actions"
import { DashboardSubdomainInfo } from "@/components/admin/dashboard-subdomain-info"
import { formatCurrency } from "@/lib/utils"
import { formatCompactNumber, formatCompactCurrency, formatCompactNumberOnly } from "@/lib/format-number"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Komponen untuk menampilkan tanggal yang hanya dirender di client-side
function FormattedDate({ date, locale = "id-ID" }: { date?: Date; locale?: string }) {
  const [formattedDate, setFormattedDate] = useState("");
  
  useEffect(() => {
    // Gunakan tanggal yang diberikan atau tanggal saat ini
    const dateToFormat = date || new Date();
    setFormattedDate(dateToFormat.toLocaleString(locale));
  }, [date, locale]);
  
  return <>{formattedDate}</>;
}

// Data dummy untuk grafik
const revenueData = [
  { name: "Jan", total: 15000000 },
  { name: "Feb", total: 22000000 },
  { name: "Mar", total: 18000000 },
  { name: "Apr", total: 25000000 },
  { name: "Mei", total: 27000000 },
  { name: "Jun", total: 32000000 },
  { name: "Jul", total: 38000000 },
]

const tenantsData = [
  { name: "Jan", total: 5 },
  { name: "Feb", total: 7 },
  { name: "Mar", total: 8 },
  { name: "Apr", total: 10 },
  { name: "Mei", total: 12 },
  { name: "Jun", total: 15 },
  { name: "Jul", total: 18 },
]

const systemStatusItems = [
  { name: "API Server", status: "online" as const, uptime: "99.9%" },
  { name: "Database", status: "online" as const, uptime: "99.8%" },
  { name: "Storage", status: "online" as const, uptime: "99.9%" },
  { name: "Payment Gateway", status: "online" as const, uptime: "99.7%" },
  { name: "Email Service", status: "warning" as const, uptime: "98.5%" },
  { name: "Cache Server", status: "online" as const, uptime: "99.9%" },
  { name: "Search Service", status: "online" as const, uptime: "99.6%" },
]

const activityItems = [
  {
    id: "1",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "menambahkan tenant baru",
    target: "Fashion Marketplace",
    date: "Baru saja",
    status: "success" as const,
  },
  {
    id: "2",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "mengupdate konfigurasi",
    target: "Payment Gateway",
    date: "5 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "3",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "melakukan restart",
    target: "API Server",
    date: "15 menit yang lalu",
    status: "info" as const,
  },
  {
    id: "4",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "menambahkan fitur baru",
    target: "Affiliate System",
    date: "30 menit yang lalu",
    status: "success" as const,
  },
  {
    id: "5",
    user: { name: "Admin System", email: "<EMAIL>" },
    action: "mengatasi error",
    target: "Email Service",
    date: "1 jam yang lalu",
    status: "error" as const,
  },
]

const quickActions = [
  {
    name: "Kelola Tenant",
    icon: Building,
    href: "/admin/dashboard/tenants",
  },
  {
    name: "Kelola Pengguna",
    icon: Users,
    href: "/admin/dashboard/users",
  },
  {
    name: "Konfigurasi Sistem",
    icon: Settings,
    href: "/admin/dashboard/settings",
  },
  {
    name: "Keamanan",
    icon: ShieldCheck,
    href: "/admin/dashboard/security",
  },
  {
    name: "Pembayaran",
    icon: CreditCard,
    href: "/admin/dashboard/payments",
  },
  {
    name: "Server",
    icon: Server,
    href: "/admin/dashboard/server",
  },
  {
    name: "Domain",
    icon: Globe,
    href: "/admin/dashboard/domains",
  },
]

export default function AdminDashboardPage() {
  const [activeTab, setActiveTab] = useState('dashboard')
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100">
      <div className="space-y-8 p-6">
        {/* Top Navigation Menu seperti gambar referensi */}
        <div className="bg-white/90 backdrop-blur-sm rounded-full pr-6 shadow-md shadow-gray-200/30 border border-white/30">
          <div className="flex items-start justify-between h-full">
            <div className="flex items-start h-full">
              <nav className="flex items-start gap-2 h-full">
                <button 
                  onClick={() => setActiveTab('dashboard')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'dashboard' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Dashboard
                </button>
                <button 
                  onClick={() => setActiveTab('tenants')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'tenants' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Tenants
                </button>
                <button 
                  onClick={() => setActiveTab('stores')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'stores' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Stores
                </button>
                <button 
                  onClick={() => setActiveTab('products')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'products' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Products
                </button>
                <button 
                  onClick={() => setActiveTab('users')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'users' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Users
                </button>
                <button 
                  onClick={() => setActiveTab('financial')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'financial' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Financial
                </button>
                <button 
                  onClick={() => setActiveTab('content')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'content' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Content
                </button>
                <button 
                  onClick={() => setActiveTab('analytics')}
                  className={`px-4 py-3 rounded-full text-sm font-medium self-start transition-all ${
                    activeTab === 'analytics' 
                      ? 'bg-gray-900 text-white' 
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  Analytics
                </button>
              </nav>
            </div>
            <div className="flex items-center gap-2">
              <button 
                onClick={() => console.log('Settings clicked')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all"
              >
                <Settings className="w-4 h-4" />
              </button>
              <button 
                onClick={() => console.log('Globe clicked')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-all"
              >
                <Globe className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Header Section dengan Welcome Message */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Welcome in, Admin
            </h1>
            <p className="text-lg text-muted-foreground">
              Dashboard Overview & Analytics
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground bg-white/60 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
              Terakhir diperbarui: <FormattedDate />
            </div>
          </div>
        </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Total Pendapatan"
            value={formatCompactCurrency(85000000)}
            description="Total pendapatan platform"
            icon={DollarSign}
            trend="up"
            trendValue="18%"
            variant="primary"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Tenant"
            value={formatCompactNumber(1800)}
            description="Total tenant aktif"
            icon={Building}
            trend="up"
            trendValue="20%"
            variant="success"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Toko"
            value={formatCompactNumber(2450)}
            description="Total toko di platform"
            icon={Store}
            trend="up"
            trendValue="15%"
            variant="warning"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[120px] w-full" />}>
          <StatCard
            title="Pengguna"
            value={formatCompactNumber(5842)}
            description="Total pengguna terdaftar"
            icon={Users}
            trend="up"
            trendValue="12%"
            variant="default"
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pendapatan Platform"
            description="Pendapatan 7 bulan terakhir"
            data={revenueData}
            type="area"
            dataKey="total"
            valueFormatter={(value) => formatCurrency(value)}
            yAxisFormatter={(value) => formatCompactNumberOnly(value)}
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ChartCard
            title="Pertumbuhan Tenant"
            description="Jumlah tenant 7 bulan terakhir"
            data={tenantsData}
            type="bar"
            dataKey="total"
            valueFormatter={(value) => `${value}`}
            colors={{
              stroke: "#3B82F6",
              fill: "url(#blueGradient)",
            }}
          />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <QuickActions
            title="Aksi Cepat"
            description="Akses cepat ke fitur utama"
            actions={quickActions}
            className="lg:col-span-2"
          />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <SystemStatus title="Status Sistem" items={systemStatusItems} />
        </Suspense>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ActivityFeed title="Aktivitas Admin" description="Aktivitas terbaru di sistem" items={activityItems} />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <DashboardSubdomainInfo />
        </Suspense>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <Card className="transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 border-border/50 bg-gradient-to-br from-card to-card/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Tenant Terbaru
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground/80">
                Tenant yang baru bergabung
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { name: "Fashion Marketplace", date: "2025-05-10", status: "active", stores: 1500, users: 12000 },
                  { name: "Electronic Store", date: "2025-05-08", status: "active", stores: 850, users: 7500 },
                  { name: "Food Delivery", date: "2025-05-05", status: "pending", stores: 0, users: 500 },
                  { name: "Book Store", date: "2025-05-01", status: "active", stores: 300, users: 2500 },
                  { name: "Sports Equipment", date: "2025-04-28", status: "active", stores: 520, users: 4000 },
                ].map((tenant, i) => (
                  <div key={i} className="flex items-center justify-between p-3 rounded-lg border border-border/30 bg-gradient-to-r from-background/50 to-background/30 hover:from-primary/5 hover:to-primary/10 transition-all duration-200 hover:border-primary/20">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                        <Building className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{tenant.name}</p>
                        <p className="text-xs text-muted-foreground/70">
                          <FormattedDate date={new Date(tenant.date)} />
                        </p>
                      </div>
                    </div>
                    <div className="text-right ml-3 flex-shrink-0">
                      <div className="flex items-center justify-end mb-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          tenant.status === "active" 
                            ? "bg-green-100 text-green-700 border border-green-200" 
                            : "bg-yellow-100 text-yellow-700 border border-yellow-200"
                        }`}>
                          {tenant.status === "active" ? "Aktif" : "Pending"}
                        </span>
                      </div>
                      <div className="flex items-center justify-end gap-2 text-xs text-muted-foreground/80">
                         <span className="flex items-center gap-1">
                           <span className="w-1 h-1 rounded-full bg-blue-500"></span>
                           <span className="font-medium text-foreground">{formatCompactNumber(tenant.stores)}</span>
                           <span>toko</span>
                         </span>
                         <span className="text-muted-foreground/50">•</span>
                         <span className="flex items-center gap-1">
                           <span className="w-1 h-1 rounded-full bg-green-500"></span>
                           <span className="font-medium text-foreground">{formatCompactNumber(tenant.users)}</span>
                           <span>user</span>
                         </span>
                       </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Suspense>
      </div>
      </div>
    </div>
  )
}
