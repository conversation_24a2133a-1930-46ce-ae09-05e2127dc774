import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching domain statistics')

    const supabase = getClient()

    // Get platform configuration for dynamic domain
    const { data: platformSettings } = await supabase
      .from('platform_settings')
      .select('key, value')
      .in('key', ['domain'])

    const platformDomain = platformSettings?.find(s => s.key === 'domain')?.value || 'sellzio.my.id'

    // Fetch all tenants with verification and SSL status
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select(`
        id, name, domain, subdomain, status, created_at,
        domain_verification_status, domain_verified_at,
        subdomain_verification_status, subdomain_verified_at,
        ssl_certificate_status, ssl_expires_at, ssl_issued_at
      `)

    if (error) {
      console.error('🔥 API: Error fetching tenant domains for stats:', error)
      return NextResponse.json(
        { error: 'Failed to fetch domain statistics' },
        { status: 500 }
      )
    }

    // Calculate statistics
    const totalTenants = tenants.length

    // Count domains: tenants that have a domain (not null)
    const totalDomains = tenants.filter(tenant => tenant.domain !== null).length

    // Count subdomains: tenants that have a subdomain (not null)
    const totalSubdomains = tenants.filter(tenant => tenant.subdomain !== null).length

    // Count verified domains based on verification status
    const verifiedDomains = tenants.filter(tenant =>
      tenant.domain !== null && tenant.domain_verification_status === 'verified'
    ).length

    // Count pending domains based on verification status
    const pendingDomains = tenants.filter(tenant =>
      tenant.domain !== null && tenant.domain_verification_status === 'pending'
    ).length

    // Count SSL certificates expiring within 30 days
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)

    const sslExpiring = tenants.filter(tenant =>
      tenant.ssl_certificate_status === 'active' &&
      tenant.ssl_expires_at &&
      new Date(tenant.ssl_expires_at) <= thirtyDaysFromNow
    ).length

    const stats = {
      totalTenants,
      totalDomains,
      totalSubdomains,
      verifiedDomains,
      pendingDomains,
      sslExpiring,
      verificationRate: totalDomains > 0 ? Math.round((verifiedDomains / totalDomains) * 100) : 0
    }

    console.log('🔥 API: Domain statistics:', stats)

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
