import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

// POST - Create custom domain request using tenants table
export async function POST(request: NextRequest) {
  try {
    const { domain, tenantId, reason } = await request.json()

    if (!domain || !tenantId) {
      return NextResponse.json(
        { error: 'Domain and tenantId are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Creating custom domain request in tenants table:', { domain, tenantId })

    const supabase = getClient()

    // Check if tenant exists and get current status
    const { data: currentTenant, error: fetchError } = await supabase
      .from('tenants')
      .select('id, name, domain, custom_domain_status, custom_domain_request')
      .eq('id', tenantId)
      .single()

    if (fetchError || !currentTenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      )
    }

    // Check if there's already a pending request
    if (currentTenant.custom_domain_status === 'pending') {
      return NextResponse.json(
        { error: 'A custom domain request is already pending approval' },
        { status: 409 }
      )
    }

    // Check if domain is already in use by another tenant
    const { data: existingDomain } = await supabase
      .from('tenants')
      .select('id, name')
      .or(`domain.eq.${domain},custom_domain_request.eq.${domain}`)
      .neq('id', tenantId)
      .limit(1)

    if (existingDomain && existingDomain.length > 0) {
      return NextResponse.json(
        { error: 'This domain is already in use or requested by another tenant' },
        { status: 409 }
      )
    }

    // Update tenant with custom domain request
    const { data, error } = await supabase
      .from('tenants')
      .update({
        custom_domain_request: domain,
        custom_domain_status: 'pending',
        custom_domain_requested_at: new Date().toISOString()
      })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error creating custom domain request:', error)
      return NextResponse.json(
        { error: 'Failed to create custom domain request' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Custom domain request created successfully:', data)

    return NextResponse.json({
      success: true,
      tenant: data,
      message: 'Custom domain request submitted successfully. Please wait for admin approval.'
    })

  } catch (error) {
    console.error('🔥 API: Error creating custom domain request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET - Get custom domain request status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')

    console.log('🔥 API: GET custom domain request status, tenantId:', tenantId)

    if (!tenantId) {
      console.log('🔥 API: Missing tenantId parameter in GET request')
      return NextResponse.json(
        { error: 'TenantId is required' },
        { status: 400 }
      )
    }

    const supabase = getClient()

    const { data: tenant, error } = await supabase
      .from('tenants')
      .select('custom_domain_request, custom_domain_status, custom_domain_requested_at, custom_domain_reviewed_at')
      .eq('id', tenantId)
      .single()

    if (error) {
      console.error('🔥 API: Error fetching custom domain request:', error)
      return NextResponse.json(
        { error: 'Failed to fetch custom domain request' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      request: {
        domain: tenant.custom_domain_request,
        status: tenant.custom_domain_status || 'none',
        requestedAt: tenant.custom_domain_requested_at,
        reviewedAt: tenant.custom_domain_reviewed_at
      }
    })

  } catch (error) {
    console.error('🔥 API: Error fetching custom domain request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Cancel custom domain request
export async function DELETE(request: NextRequest) {
  try {
    const { tenantId } = await request.json()

    if (!tenantId) {
      return NextResponse.json(
        { error: 'TenantId is required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Cancelling custom domain request for tenant:', tenantId)

    const supabase = getClient()

    // Check if tenant exists and has pending request
    const { data: currentTenant, error: fetchError } = await supabase
      .from('tenants')
      .select('id, name, custom_domain_request, custom_domain_status')
      .eq('id', tenantId)
      .single()

    if (fetchError || !currentTenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      )
    }

    if (currentTenant.custom_domain_status !== 'pending') {
      return NextResponse.json(
        { error: 'No pending custom domain request found' },
        { status: 404 }
      )
    }

    // Clear custom domain request
    const { data, error } = await supabase
      .from('tenants')
      .update({
        custom_domain_request: null,
        custom_domain_status: null,
        custom_domain_requested_at: null,
        custom_domain_reviewed_at: null
      })
      .eq('id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error cancelling custom domain request:', error)
      return NextResponse.json(
        { error: 'Failed to cancel custom domain request' },
        { status: 500 }
      )
    }

    console.log('🔥 API: Custom domain request cancelled successfully:', data)

    return NextResponse.json({
      success: true,
      tenant: data,
      message: 'Custom domain request cancelled successfully.'
    })

  } catch (error) {
    console.error('🔥 API: Error cancelling custom domain request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
