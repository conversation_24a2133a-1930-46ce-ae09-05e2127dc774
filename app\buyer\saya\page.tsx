"use client"

import { useState } from "react"
import Link from "next/link"
import {
  User,
  Bell,
  ShoppingBag,
  Package,
  Truck,
  Award,
  DollarSign,
  FileText,
  Settings,
  MessageCircle,
  Clock,
  CheckCircle,
  CreditCard,
  Smartphone,
  Utensils,
  ChevronRight,
  X,
  Camera,
  MapPin,
  Heart,
  Star,
  Shield
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useAuth } from "@/contexts/auth-context"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"

export default function SayaPage() {
  const { user } = useAuth()
  const isMobile = useIsMobile()
  const [showNotification, setShowNotification] = useState(true)

  if (!isMobile) {
    return (
      <div className="container mx-auto p-6">
        <h1>Desktop version coming soon</h1>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Orange Header dengan <PERSON>tern */}
      <div className="relative bg-gradient-to-r from-orange-500 to-red-500 overflow-hidden">
        {/* Pattern Background */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* Header Content */}
        <div className="relative px-4 py-6">
          {/* Top Bar */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2 text-white">
              <div className="w-6 h-6 border border-white rounded flex items-center justify-center">
                <span className="text-xs">📍</span>
              </div>
              <span className="text-sm font-medium">Toko Saya</span>
              <ChevronRight className="w-4 h-4" />
            </div>
            <div className="flex items-center gap-4">
              <Settings className="w-6 h-6 text-white" />
              <div className="relative">
                <ShoppingBag className="w-6 h-6 text-white" />
                <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                  45
                </Badge>
              </div>
              <MessageCircle className="w-6 h-6 text-white" />
            </div>
          </div>

          {/* Profile Section */}
          <div className="flex items-center gap-3">
            <div className="relative">
              <Avatar className="w-12 h-12 border-2 border-white">
                <AvatarImage src="/placeholder-avatar.jpg" />
                <AvatarFallback className="bg-yellow-400 text-white font-bold">
                  {user?.email?.[0]?.toUpperCase() || 'S'}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                <Camera className="w-3 h-3 text-white" />
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="text-white font-semibold text-lg">sofeafarm</span>
                <Badge className="bg-yellow-400 text-yellow-900 text-xs px-2 py-0.5 rounded">
                  Gold
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-white text-sm">
                <span>1 Pengikut</span>
                <span>25 Mengikuti</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification Banner */}
      {showNotification && (
        <div className="bg-white mx-4 mt-4 p-3 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-start gap-3">
            <Bell className="w-5 h-5 text-orange-500 mt-0.5" />
            <div className="flex-1">
              <p className="text-sm text-gray-700">
                Izinkan notifikasi untuk dapatkan info status pesanan, promo, dan info menarik lainnya.
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                className="bg-orange-500 hover:bg-orange-600 text-white text-xs px-3 py-1"
              >
                Izinkan
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowNotification(false)}
                className="p-1"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="px-4 py-4 space-y-6">
        {/* Pesanan Saya */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h2 className="font-semibold text-gray-900">Pesanan Saya</h2>
            <Link href="/buyer/orders" className="flex items-center gap-1 text-sm text-gray-600">
              <span>Lihat Riwayat Pesanan</span>
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>
          <div className="grid grid-cols-4 gap-4">
            <Link href="/buyer/orders?status=pending" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <Package className="w-8 h-8 text-gray-600" />
              </div>
              <span className="text-xs text-gray-700 text-center">Belum Bayar</span>
            </Link>
            <Link href="/buyer/orders?status=processing" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <Clock className="w-8 h-8 text-gray-600" />
              </div>
              <span className="text-xs text-gray-700 text-center">Dikemas</span>
            </Link>
            <Link href="/buyer/orders?status=shipped" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <Truck className="w-8 h-8 text-gray-600" />
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center p-0">
                  1
                </Badge>
              </div>
              <span className="text-xs text-gray-700 text-center">Dikirim</span>
            </Link>
            <Link href="/buyer/orders?status=completed" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <CheckCircle className="w-8 h-8 text-gray-600" />
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center p-0">
                  2
                </Badge>
              </div>
              <span className="text-xs text-gray-700 text-center">Beri Penilaian</span>
            </Link>
          </div>
        </div>

        {/* Pulsa, Tagihan & Tiket */}
        <Link href="/buyer/pulsa" className="flex items-center justify-between p-4 bg-white rounded-lg">
          <div className="flex items-center gap-3">
            <Smartphone className="w-6 h-6 text-blue-500" />
            <span className="font-medium text-gray-900">Pulsa, Tagihan & Tiket</span>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </Link>

        {/* ShopeeFood */}
        <Link href="/buyer/food" className="flex items-center justify-between p-4 bg-white rounded-lg">
          <div className="flex items-center gap-3">
            <Utensils className="w-6 h-6 text-orange-500" />
            <span className="font-medium text-gray-900">ShopeeFood</span>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </Link>

        {/* Dompet Saya */}
        <div>
          <h2 className="font-semibold text-gray-900 mb-3">Dompet Saya</h2>
          <div className="grid grid-cols-4 gap-4">
            <Link href="/buyer/wallet/shopeepay" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <DollarSign className="w-8 h-8 text-orange-500" />
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center p-0">
                  !
                </Badge>
              </div>
              <span className="text-xs text-gray-700 text-center">ShopeePay</span>
              <span className="text-xs text-orange-500 font-medium">Rp1.164</span>
            </Link>
            <Link href="/buyer/wallet/coins" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <Award className="w-8 h-8 text-yellow-500" />
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center p-0">
                  !
                </Badge>
              </div>
              <span className="text-xs text-gray-700 text-center">Koin Shopee</span>
              <span className="text-xs text-orange-500 font-medium">Gratis 25RB!</span>
            </Link>
            <Link href="/buyer/wallet/vouchers" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <div className="relative">
                <FileText className="w-8 h-8 text-green-500" />
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center p-0">
                  !
                </Badge>
              </div>
              <span className="text-xs text-gray-700 text-center">Voucher Saya</span>
              <span className="text-xs text-orange-500 font-medium">50+ Voucher</span>
            </Link>
            <Link href="/buyer/wallet/installment" className="flex flex-col items-center gap-2 p-3 bg-white rounded-lg">
              <CreditCard className="w-8 h-8 text-blue-500" />
              <span className="text-xs text-gray-700 text-center">Instal ShopeePay</span>
              <span className="text-xs text-orange-500 font-medium">Ambil 50.000</span>
            </Link>
          </div>
        </div>

        {/* Keuangan */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h2 className="font-semibold text-gray-900">Keuangan</h2>
            <Link href="/buyer/finance" className="flex items-center gap-1 text-sm text-gray-600">
              <span>Lihat Semua</span>
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Link href="/buyer/finance/spaylater" className="flex items-center gap-3 p-4 bg-white rounded-lg">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 text-sm">SPayLater</h3>
                <p className="text-xs text-gray-500">Diskon 100%</p>
                <Badge className="bg-red-100 text-red-600 text-xs px-2 py-0.5 rounded mt-1">
                  Baru
                </Badge>
              </div>
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </Link>
            <Link href="/buyer/finance/spinjam" className="flex items-center gap-3 p-4 bg-white rounded-lg">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-orange-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 text-sm">SPinjam</h3>
                <p className="text-xs text-gray-500">Hingga 100JT</p>
              </div>
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </Link>
            <Link href="/buyer/finance/seabank" className="flex items-center gap-3 p-4 bg-white rounded-lg">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Award className="w-6 h-6 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 text-sm">SeaBank</h3>
                <p className="text-xs text-gray-500">Gratis Transfer Antar Bank</p>
              </div>
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </Link>
            <Link href="/buyer/finance/insurance" className="flex items-center gap-3 p-4 bg-white rounded-lg">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-purple-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 text-sm">Asuransi</h3>
                <p className="text-xs text-gray-500">Proteksi Terbaik</p>
              </div>
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
