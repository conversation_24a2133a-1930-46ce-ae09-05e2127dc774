"use client"

import Link from "next/link"
import { Package, Heart, Award, MapPin, DollarSign, FileText, Truck, Star } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface QuickStatsProps {
  className?: string
}

export function QuickStats({ className }: QuickStatsProps) {
  const stats = [
    {
      title: "Orders",
      value: "3",
      subtitle: "2 In Progress",
      icon: Package,
      href: "/buyer/dashboard/orders",
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Wishlist",
      value: "12",
      subtitle: "Saved items",
      icon: Heart,
      href: "/buyer/dashboard/wishlist",
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      title: "Rewards",
      value: "750",
      subtitle: "Silver Tier",
      icon: Award,
      href: "/buyer/dashboard/rewards",
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    },
    {
      title: "Addresses",
      value: "2",
      subtitle: "Home, Office",
      icon: MapPin,
      href: "/buyer/dashboard/account/addresses",
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
  ]

  return (
    <div className={cn("mx-2", className)}>
      <Card className="shadow-sm">
        <CardContent className="p-4">
          <h3 className="text-sm font-medium text-gray-600 mb-3">Quick Stats</h3>
          <div className="grid grid-cols-2 gap-3">
            {stats.map((stat, index) => (
              <Link 
                key={index}
                href={stat.href}
                className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center mr-3", stat.bgColor)}>
                  <stat.icon className={cn("w-5 h-5", stat.color)} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold text-gray-900">{stat.value}</span>
                    <span className="text-xs text-gray-500">{stat.title}</span>
                  </div>
                  <p className="text-xs text-gray-500 truncate">{stat.subtitle}</p>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default QuickStats
