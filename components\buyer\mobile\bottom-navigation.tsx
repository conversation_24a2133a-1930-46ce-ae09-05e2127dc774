"use client"

import { useState, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { Home, ShoppingBag, Package, Heart, User } from "lucide-react"
import { cn } from "@/lib/utils"

export function BottomNavigation() {
  const pathname = usePathname()
  const router = useRouter()
  const [activeItem, setActiveItem] = useState<string>("")

  useEffect(() => {
    // Extract the main section from the path
    const pathSegments = pathname.split("/")
    const mainSection = pathSegments.length > 2 ? pathSegments[3] : ""

    if (mainSection === "" || mainSection === "dashboard") {
      setActiveItem("home")
    } else if (mainSection === "marketplace") {
      setActiveItem("shop")
    } else if (mainSection === "orders") {
      setActiveItem("orders")
    } else if (mainSection === "wishlist") {
      setActiveItem("wishlist")
    } else if (mainSection === "account" || mainSection === "profile" || mainSection === "settings" || mainSection === "saya") {
      setActiveItem("account")
    }
  }, [pathname])

  const navigateTo = (path: string) => {
    router.push(path)
  }

  const navItems = [
    {
      name: "home",
      icon: Home,
      label: "Home",
      path: "/buyer/dashboard",
    },
    {
      name: "shop",
      icon: ShoppingBag,
      label: "Shop",
      path: "/buyer/dashboard/marketplace",
    },
    {
      name: "orders",
      icon: Package,
      label: "Orders",
      path: "/buyer/dashboard/orders",
    },
    {
      name: "wishlist",
      icon: Heart,
      label: "Wishlist",
      path: "/buyer/dashboard/wishlist",
    },
    {
      name: "account",
      icon: User,
      label: "Saya",
      path: "/buyer/saya",
    },
  ]

  return (
    <div className="fixed bottom-0 left-0 z-50 w-full h-16 bg-white border-t border-gray-200 dark:bg-gray-800 dark:border-gray-700">
      <div className="grid h-full grid-cols-5">
        {navItems.map((item) => (
          <button
            key={item.name}
            type="button"
            className={cn(
              "inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-700",
              activeItem === item.name && "bg-gray-50 dark:bg-gray-700",
            )}
            onClick={() => navigateTo(item.path)}
          >
            <item.icon
              className={cn(
                "w-6 h-6 mb-1",
                activeItem === item.name ? "text-primary" : "text-gray-500 dark:text-gray-400",
              )}
            />
            <span
              className={cn(
                "text-xs",
                activeItem === item.name ? "text-primary font-medium" : "text-gray-500 dark:text-gray-400",
              )}
            >
              {item.label}
            </span>
          </button>
        ))}
      </div>
    </div>
  )
}

// Ensure we have both default and named exports
export default BottomNavigation
