import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import "./sellzio-styles.css"

interface FacetData {
  categories: Record<string, number>
  priceRanges: Record<string, number>
  ratings: Record<string, number>
  shipping: Record<string, number>
  features: Record<string, number>
  provinces: Record<string, number>
}

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
  // Tambahkan properti untuk facet filter
  kategori?: string[]
  'rentang harga'?: string[]
  rating?: string[]
  pengiriman?: string[]
  fitur?: string[]
  provinsi?: string[]
  kota?: string[]
}

interface SellzioFacetProps {
  searchResults: any[]
  displayedProducts?: any[] // Add this for calculating facet data from displayed products
  activeFilters: ActiveFilters
  onFiltersChange: (filters: ActiveFilters) => void
  isVisible: boolean
  onClose: () => void
  isDesktopSidebar?: boolean
  allProducts?: any[] // Add this to access all products for counting
  subcategoryContext?: {
    category: string
    selectedSubcategory: string
    allSubcategories: Array<{
      id: string
      name: string
      icon?: string
      color?: string
    }>
  } | null
}

export function SellzioFacet({
  searchResults,
  displayedProducts,
  activeFilters,
  onFiltersChange,
  isVisible,
  onClose,
  isDesktopSidebar = false,
  allProducts = [],
  subcategoryContext
}: SellzioFacetProps) {
  const [tempFilters, setTempFilters] = useState<ActiveFilters>(activeFilters)
  const [facetData, setFacetData] = useState<FacetData>({
    categories: {},
    priceRanges: {},
    ratings: {},
    shipping: {},
    features: {},
    provinces: {}
  })
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isSubcategoriesExpanded, setIsSubcategoriesExpanded] = useState(false)
  const [isCitiesExpanded, setIsCitiesExpanded] = useState(false)
  const [expandedProvinces, setExpandedProvinces] = useState<Record<string, boolean>>({})
  const [sortedSubcategories, setSortedSubcategories] = useState<string[]>([])


  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768) // Mobile: < 768px
      setIsTablet(width >= 768 && width < 1025) // Tablet: 768px - 1024px
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Extract facets from search results
  useEffect(() => {
    console.log('🎯 FACET useEffect triggered:', {
      searchResultsCount: searchResults?.length || 0,
      displayedProductsCount: displayedProducts?.length || 0,
      willUse: 'searchResults (always use original for facet options)'
    });

    // PERBAIKAN: Selalu gunakan searchResults asli untuk menghitung facet options
    // Jangan gunakan displayedProducts karena akan menyebabkan filter options hilang
    const productsForFacets = searchResults;
    console.log('🎯 FACET useEffect: Using original searchResults for facet calculation');

    const facets = extractFacets(productsForFacets)
    console.log('🎯 FACET useEffect: Setting facetData:', facets);
    setFacetData(facets)
  }, [searchResults, subcategoryContext])

  // Reset temp filters when activeFilters change
  useEffect(() => {
    console.log('🔄 FACET: activeFilters changed, updating tempFilters:', activeFilters);
    setTempFilters({ ...activeFilters });
  }, [activeFilters])

  // Track previous subcategory to detect changes
  const [prevSubcategory, setPrevSubcategory] = useState<string | null>(null);

  // Initialize sorted subcategories when context changes (from subcategory view clicks)
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    if (context && context.allSubcategories) {
      const subcategoryNames = context.allSubcategories.map((sub: any) => sub.name);

      // Set selected subcategory if available (from subcategory view click)
      if (context.selectedSubcategory) {
        // Move selected to top ONLY when coming from subcategory view
        const reordered = [
          context.selectedSubcategory,
          ...subcategoryNames.filter((name: string) => name !== context.selectedSubcategory)
        ];
        setSortedSubcategories(reordered);
      } else {
        // No selection from subcategory view, use original order
        setSortedSubcategories(subcategoryNames);
      }
    }
  }, [subcategoryContext]);

  // FIXED: Remove auto-reset logic that causes checkbox revert on mobile/tablet
  // This useEffect was causing tempFilters to reset automatically
  // useEffect(() => {
  //   const context = subcategoryContext || (window as any).subcategoryContext;
  //   const currentSubcategory = context?.selectedSubcategory;
  //   const isActiveFiltersEmpty = Object.keys(activeFilters).length === 0;
  //   if (currentSubcategory && currentSubcategory !== prevSubcategory && !isActiveFiltersEmpty) {
  //     const newFilters = { kategori: [currentSubcategory] };
  //     setTempFilters(newFilters);
  //     setPrevSubcategory(currentSubcategory);
  //   } else if (isActiveFiltersEmpty) {
  //     setPrevSubcategory(currentSubcategory);
  //   }
  // }, [subcategoryContext, prevSubcategory, activeFilters])

  // FIXED: Remove auto-apply useEffect that causes checkbox revert
  // This was causing tempFilters changes to auto-apply on mobile/tablet
  // useEffect(() => {
  //   if (isDesktopSidebar && tempFilters.kategori) {
  //     const timeoutId = setTimeout(() => {
  //       onFiltersChange(tempFilters);
  //     }, 100);
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [tempFilters, isDesktopSidebar, onFiltersChange])

  // Helper function to detect main category from search results
  const detectMainCategoryFromResults = (results: any[]): { name: string; subcategories: any[] } | null => {
    if (!results || results.length === 0) return null;

    // Category mappings with subcategories
    const categoryMappings = {
      'Elektronik': [
        'Konsol Game', 'Aksesoris Konsol', 'Alat Casing', 'Foot Bath & Spa',
        'Mesin Jahit & Aksesoris', 'Setrika & Mesin Uap', 'Purifier & Humidifier',
        'Perangkat Debu & Peralatan Perawatan Lantai', 'Telepon', 'Mesin Cuci & Pengering',
        'Water Heater', 'Pendingin Ruangan', 'Pengering Sepatu', 'Penghangat Ruangan',
        'TV & Aksesoris', 'Perangkat Dapur', 'Lampu', 'Kamera Keamanan',
        'Video Game', 'Kelastrian', 'Baterai', 'Rokok Elektronik & Shisha',
        'Remote Kontrol', 'Walkie Talkie', 'Media Player', 'Perangkat Audio & Speaker',
        'Elektronik Lainnya'
      ],
      'Komputer & Aksesoris': [
        'Laptop', 'Komputer', 'Aksesoris Komputer', 'Software', 'Monitor', 'Keyboard', 'Mouse'
      ],
      'Handphone & Aksesoris': [
        'Handphone', 'Smartphone', 'Aksesoris Handphone', 'Tablet', 'Case Handphone', 'Charger'
      ],
      'Pakaian Pria': [
        'Kemeja', 'Celana', 'Jaket', 'Kaos', 'Pakaian Dalam', 'Sweater', 'Hoodie'
      ],
      'Sepatu Pria': [
        'Sepatu Formal', 'Sepatu Casual', 'Sepatu Olahraga', 'Sandal', 'Boots'
      ],
      'Tas Pria': [
        'Tas Kerja', 'Tas Casual', 'Dompet', 'Ransel', 'Tas Laptop', 'Tas Travel'
      ],
      'Aksesoris Fashion': [
        'Jam Tangan', 'Kacamata', 'Topi', 'Ikat Pinggang', 'Kalung', 'Gelang'
      ],
      'Jam Tangan': [
        'Jam Tangan Pria', 'Jam Tangan Wanita', 'Smartwatch', 'Jam Digital', 'Jam Analog'
      ],
      'Kesehatan': [
        'Vitamin', 'Suplemen', 'Alat Kesehatan', 'Obat-obatan', 'Masker', 'Hand Sanitizer'
      ],
      'Hobi & Koleksi': [
        'Mainan', 'Koleksi', 'Buku', 'Alat Musik', 'Puzzle', 'Board Game'
      ]
    };

    // Count products by category
    const categoryCounts: { [key: string]: number } = {};

    results.forEach(product => {
      // Use the main category directly from product.category
      if (product.category) {
        const mainCategory = product.category;
        categoryCounts[mainCategory] = (categoryCounts[mainCategory] || 0) + 1;
      }
    });

    // Find the main category with most products
    let dominantCategory = null;
    let maxCount = 0;

    for (const [category, count] of Object.entries(categoryCounts)) {
      if (count > maxCount) {
        maxCount = count;
        dominantCategory = category;
      }
    }

    // Return category info if we found a dominant category
    if (dominantCategory && maxCount > 0) {
      // Pastikan categoryMappings ada dan memiliki dominantCategory
      const subcategories = categoryMappings && (categoryMappings as any)[dominantCategory]
        ? (categoryMappings as any)[dominantCategory].map((name: string) => ({ name, icon: '📦' }))
        : []

      return {
        name: dominantCategory,
        subcategories
      };
    }

    return null;
  };

  const extractFacets = (results: any[]): FacetData => {
    console.log('🎯 FACET-EXTRACT: Starting extraction with results:', results?.length || 0);
    if (!results || !Array.isArray(results)) {
      console.log('🎯 FACET-EXTRACT: No results provided, using empty array');
      results = [];
    }

    // Debug sample products
    if (results.length > 0) {
      console.log('🎯 FACET-EXTRACT: Sample products:', results.slice(0, 3).map(p => ({
        name: p.name,
        category: p.category,
        subcategory: p.subcategory
      })));
    }

    // PERBAIKAN: Gunakan displayedProducts untuk menghitung count yang akurat
    // tapi tetap gunakan searchResults untuk mendapatkan semua opsi filter
    const productsForCounting = displayedProducts || results;
    console.log('🎯 FACET-EXTRACT: Using for counting:', productsForCounting?.length || 0, 'products');
    console.log('🎯 FACET-EXTRACT: Function called with results:', results.length);
    // Use subcategory context from props or window
    const context = subcategoryContext || (window as any).subcategoryContext;
    console.log('🎯 FACET-EXTRACT: Context:', context);

    // ENHANCED: Create context for regular search to enable hierarchical display
    let effectiveContext = context;

    // If no context exists, create one based on search results for hierarchical display
    if (!context && results.length > 0) {
      // Detect main category from search results
      const detectedCategory = detectMainCategoryFromResults(results);
      if (detectedCategory) {
        effectiveContext = {
          category: detectedCategory.name,
          allSubcategories: detectedCategory.subcategories,
          selectedSubcategory: null // No specific subcategory selected in regular search
        };
      }
    }

    // Always show all subcategories if we have context (original or created)
    const shouldShowAllSubcategories = effectiveContext && effectiveContext.allSubcategories;

    // PERBAIKAN: Ubah kondisi agar tidak menghalangi processing normal
    if (false) { // shouldShowAllSubcategories - DISABLED
      // If we have subcategory context, use it for categories
      let categories = {
        "Handphone & Tablet": 45,
        "Elektronik": 32,
        "Fashion Pria": 28,
        "Fashion Wanita": 41,
        "Tas & Travel": 19,
        "Sepatu": 23,
        "Aksesoris Fashion": 15
      };

      // Override with subcategory data if available
      if (effectiveContext && effectiveContext.allSubcategories && effectiveContext.allSubcategories.length > 0) {
        const dynamicCategories: { [key: string]: number } = {};

        // Calculate actual product counts for each subcategory based on SMART logic
        let totalCategoryCount = 0;

        // Check if non-category filters are applied (including location filters)
        const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                     (tempFilters.rating?.length || 0) > 0 ||
                                     (tempFilters.pengiriman?.length || 0) > 0 ||
                                     (tempFilters.fitur?.length || 0) > 0 ||
                                     (tempFilters.provinsi?.length || 0) > 0 ||
                                     (tempFilters.kota?.length || 0) > 0;

        effectiveContext.allSubcategories.forEach((sub: any) => {
          // SMART LOGIC: Use filtered products ONLY if non-category filters are applied
          const productsToCount = hasNonCategoryFilters ? results : searchResults;

          const subcategoryCount = productsToCount.filter((product: any) => {
            const subName = sub.name.toLowerCase();
            const productSubcategory = product.subcategory?.toLowerCase() || '';

            // FIXED: Use subcategory field for exact matching
            return productSubcategory === subName;
          }).length;

          // Show all subcategories with their actual product count (even if 0)
          const count = subcategoryCount;
          dynamicCategories[sub.name] = count;
          totalCategoryCount += count;
        });

        // Add main category with total count from all subcategories
        dynamicCategories[effectiveContext.category] = totalCategoryCount;

        categories = dynamicCategories as typeof categories;
      } else {

        // Fallback: Check if we're in Elektronik category based on search results
        const hasElektronikProducts = searchResults.some(product =>
          product.category && product.category.toLowerCase() === 'elektronik'
        );

        if (hasElektronikProducts) {
          const elektronikSubcategories = [
            "Konsol Game", "Aksesoris Konsol", "Alat Casing", "Foot Bath & Spa",
            "Mesin Jahit & Aksesoris", "Setrika & Mesin Uap", "Purifier & Humidifier",
            "Perangkat Debu & Peralatan Perawatan Lantai", "Telepon", "Mesin Cuci & Pengering",
            "Water Heater", "Pendingin Ruangan", "Pengering Sepatu", "Penghangat Ruangan",
            "TV & Aksesoris", "Perangkat Dapur", "Lampu", "Kamera Keamanan",
            "Video Game", "Kelastrian", "Baterai", "Rokok Elektronik & Shisha",
            "Remote Kontrol", "Walkie Talkie", "Media Player", "Perangkat Audio & Speaker",
            "Elektronik Lainnya"
          ];

          const dynamicCategories: { [key: string]: number } = {};

          // Add main category - calculate from actual subcategory counts
          let totalElektronikCount = 0;

          // Check if non-category filters are applied (same logic as above, including location filters)
          const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                       (tempFilters.rating?.length || 0) > 0 ||
                                       (tempFilters.pengiriman?.length || 0) > 0 ||
                                       (tempFilters.fitur?.length || 0) > 0 ||
                                       (tempFilters.provinsi?.length || 0) > 0 ||
                                       (tempFilters.kota?.length || 0) > 0;

          // Add all subcategories with real counts
          elektronikSubcategories.forEach((subName) => {
            // SMART LOGIC: Use filtered products ONLY if non-category filters are applied
            const productsToCount = hasNonCategoryFilters ? results : searchResults;
            const subcategoryCount = productsToCount.filter((product: any) => {
              const subNameLower = subName.toLowerCase();
              const productSubcategory = product.subcategory?.toLowerCase() || '';

              // FIXED: Use subcategory field for exact matching
              return productSubcategory === subNameLower;
            }).length;

            dynamicCategories[subName] = subcategoryCount;
            totalElektronikCount += subcategoryCount;
          });

          // Set main category count as sum of all subcategories
          dynamicCategories["Elektronik"] = totalElektronikCount;

          categories = dynamicCategories as typeof categories;
        }
      }

      // FIXED: Calculate real data for all facets based on displayed products
      const realFacets = {
        priceRanges: {
          "Di bawah Rp 100.000": 0,
          "Rp 100.000 - Rp 500.000": 0,
          "Rp 500.000 - Rp 1.000.000": 0,
          "Rp 1.000.000 - Rp 5.000.000": 0,
          "Di atas Rp 5.000.000": 0
        },
        ratings: {
          "5 Bintang": 0,
          "4 Bintang ke atas": 0,
          "3 Bintang ke atas": 0
        },
        shipping: {
          "Gratis Ongkir": 0,
          "Same Day": 0,
          "Next Day": 0
        },
        features: {
          "COD": 0,
          "SellZio Mall": 0,
          "Flash Sale": 0
        },
        provinces: {} as Record<string, number>
      };

      // Calculate real counts from displayed products (for price, rating, shipping, features, cities)
      // Note: results parameter here is already displayedProducts from useEffect
      results.forEach(product => {
        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          realFacets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          realFacets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          realFacets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          realFacets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          realFacets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) realFacets.ratings["5 Bintang"]++
        if (rating >= 4) realFacets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) realFacets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") realFacets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") realFacets.shipping["Same Day"]++
        if (product.shipping === "Next Day") realFacets.shipping["Next Day"]++

        // Features
        if (product.cod === true) realFacets.features["COD"]++
        if (product.isMall === true) realFacets.features["SellZio Mall"]++
        if (product.flashSale === true) realFacets.features["Flash Sale"]++

        // Provinces
        if (product.address && product.address.province) {
          const provinceName = product.address.province as string;
          realFacets.provinces[provinceName] = (realFacets.provinces[provinceName] || 0) + 1
        }
      });



      // PERBAIKAN: Return disabled, lanjut ke processing normal
      // return statement di-disable agar lanjut ke loop utama
    }

    const facets: FacetData = {
      categories: {},
      priceRanges: {
        "Di bawah Rp 100.000": 0,
        "Rp 100.000 - Rp 500.000": 0,
        "Rp 500.000 - Rp 1.000.000": 0,
        "Rp 1.000.000 - Rp 5.000.000": 0,
        "Di atas Rp 5.000.000": 0
      },
      ratings: {
        "5 Bintang": 0,
        "4 Bintang ke atas": 0,
        "3 Bintang ke atas": 0
      },
      shipping: {
        "Gratis Ongkir": 0,
        "Same Day": 0,
        "Next Day": 0
      },
      features: {
        "COD": 0,
        "SellZio Mall": 0,
        "Flash Sale": 0
      },
      provinces: {}
    }

    // PERBAIKAN: Hitung semua opsi dari data asli, tapi count dari data yang difilter
    console.log('🎯 FACET-EXTRACT: Processing products for facet calculation');

    // Pertama, kumpulkan semua opsi yang tersedia dari data asli
    const allOptions = {
      categories: new Set<string>(),
      provinces: new Set<string>(),
      cities: new Set<string>()
    };

    results.forEach(product => {
      // Kumpulkan semua kategori
      if (product.category) {
        allOptions.categories.add(product.category);
      }
      if (product.subcategory) {
        allOptions.categories.add(product.subcategory);
      }

      // Kumpulkan semua provinsi dan kota
      if (product.address?.province) {
        allOptions.provinces.add(product.address.province);
      }
      if (product.address?.city) {
        allOptions.cities.add(product.address.city);
      }
    });

    // Inisialisasi semua opsi dengan count 0
    allOptions.categories.forEach(cat => {
      facets.categories[cat] = 0;
    });
    allOptions.provinces.forEach(prov => {
      facets.provinces[prov] = 0;
    });

    console.log('🎯 FACET-EXTRACT: All available options:', {
      categories: Array.from(allOptions.categories),
      provinces: Array.from(allOptions.provinces),
      cities: Array.from(allOptions.cities)
    });

    // ENHANCED: Always process categories for regular search (including multi-category)
    console.log('🎯 FACET-EXTRACT: Checking condition - context:', !!context, 'allSubcategories:', !!context?.allSubcategories);

    // PERBAIKAN: Hitung count berdasarkan produk yang difilter, bukan semua produk
    console.log('🎯 FACET-EXTRACT: Counting from filtered products:', productsForCounting.length);
    productsForCounting.forEach((product, index) => {
      console.log('🎯 FACET-EXTRACT: Product', index, 'data:', {
        name: product.name,
        category: product.category,
        subcategory: product.subcategory,
        hasCategory: !!product.category,
        hasSubcategory: !!product.subcategory
      });

      // PERBAIKAN: Count kategori bahkan jika hanya ada category (tanpa subcategory)
      if (product.category) {
        // Count main category
        facets.categories[product.category] = (facets.categories[product.category] || 0) + 1;
        console.log('🎯 FACET-EXTRACT: Added main category:', product.category, 'new count:', facets.categories[product.category]);

        // Count subcategory jika ada
        if (product.subcategory) {
          facets.categories[product.subcategory] = (facets.categories[product.subcategory] || 0) + 1;
          console.log('🎯 FACET-EXTRACT: Added subcategory:', product.subcategory, 'new count:', facets.categories[product.subcategory]);
        }

        console.log('🎯 FACET-EXTRACT: Current categories after product', index, ':', facets.categories);
      }

      // Price ranges
      const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
      if (price < 100000) {
        facets.priceRanges["Di bawah Rp 100.000"]++
      } else if (price < 500000) {
        facets.priceRanges["Rp 100.000 - Rp 500.000"]++
      } else if (price < 1000000) {
        facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
      } else if (price < 5000000) {
        facets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
      } else {
        facets.priceRanges["Di atas Rp 5.000.000"]++
      }

      // Ratings
      const rating = product.rating || 0
      if (rating >= 5) facets.ratings["5 Bintang"]++
      if (rating >= 4) facets.ratings["4 Bintang ke atas"]++
      if (rating >= 3) facets.ratings["3 Bintang ke atas"]++

      // Shipping
      if (product.shipping === "Gratis Ongkir") facets.shipping["Gratis Ongkir"]++
      if (product.shipping === "Same Day") facets.shipping["Same Day"]++
      if (product.shipping === "Next Day") facets.shipping["Next Day"]++

      // Features
      if (product.cod === true) facets.features["COD"]++
      if (product.isMall === true) facets.features["SellZio Mall"]++
      if (product.flashSale === true) facets.features["Flash Sale"]++

      // FIXED: Provinces - use displayedProducts for accurate province counts
      if (product.address && product.address.province) {
        const provinceName = product.address.province as string;
        facets.provinces[provinceName] = (facets.provinces[provinceName] || 0) + 1
      }
    });

    console.log('🎯 FACET-EXTRACT: Final categories after processing all products:', facets.categories);

    // PERBAIKAN: Tambahkan semua subkategori yang hilang dengan count 0
    // Jika ada produk Elektronik, tampilkan semua subkategori Elektronik
    if (facets.categories['Elektronik'] && facets.categories['Elektronik'] > 0) {
      // Gunakan daftar subkategori yang sesuai dengan database
      const elektronikSubcategories = [
        "Audio & Wearables", "Gaming & Console", "Smartphone & Tablet",
        "Komputer & Laptop", "Kamera & Fotografi", "TV & Audio",
        "Elektronik Rumah", "Aksesoris Elektronik", "Perangkat Rumah Pintar",
        "Elektronik Lainnya"
      ];

      console.log('🎯 FACET-EXTRACT: Adding missing Elektronik subcategories...');

      elektronikSubcategories.forEach(subcat => {
        if (!facets.categories[subcat]) {
          facets.categories[subcat] = 0;
          console.log('🎯 FACET-EXTRACT: Added missing subcategory:', subcat, 'with count 0');
        }
      });
    }

    // Tambahkan logika untuk kategori lain jika diperlukan
    // Fashion Pria
    if (facets.categories['Fashion Pria'] && facets.categories['Fashion Pria'] > 0) {
      console.log('🎯 FACET-EXTRACT: Adding missing Fashion Pria subcategories...');
      const fashionPriaSubcategories = [
        "Pakaian Pria", "Sepatu Pria", "Aksesoris Pria", "Tas Pria", "Jam Tangan Pria"
      ];
      fashionPriaSubcategories.forEach(subcat => {
        if (!facets.categories[subcat]) {
          facets.categories[subcat] = 0;
          console.log('🎯 FACET-EXTRACT: Added missing Fashion Pria subcategory:', subcat);
        }
      });
    }

    // Fashion Wanita
    if (facets.categories['Fashion Wanita'] && facets.categories['Fashion Wanita'] > 0) {
      console.log('🎯 FACET-EXTRACT: Adding missing Fashion Wanita subcategories...');
      const fashionWanitaSubcategories = [
        "Pakaian Wanita", "Sepatu Wanita", "Aksesoris Wanita", "Tas Wanita", "Jam Tangan Wanita"
      ];
      fashionWanitaSubcategories.forEach(subcat => {
        if (!facets.categories[subcat]) {
          facets.categories[subcat] = 0;
          console.log('🎯 FACET-EXTRACT: Added missing Fashion Wanita subcategory:', subcat);
        }
      });
    }

    // Kesehatan & Kecantikan
    if (facets.categories['Kesehatan & Kecantikan'] && facets.categories['Kesehatan & Kecantikan'] > 0) {
      console.log('🎯 FACET-EXTRACT: Adding missing Kesehatan & Kecantikan subcategories...');
      const kesehatanSubcategories = [
        "Perawatan Wajah", "Makeup", "Perawatan Rambut", "Perawatan Tubuh", "Suplemen & Vitamin"
      ];
      kesehatanSubcategories.forEach(subcat => {
        if (!facets.categories[subcat]) {
          facets.categories[subcat] = 0;
          console.log('🎯 FACET-EXTRACT: Added missing Kesehatan & Kecantikan subcategory:', subcat);
        }
      });
    }

    console.log('🎯 FACET-EXTRACT: Categories after adding missing subcategories:', facets.categories);

    console.log('🎯 FACET-EXTRACT: Final facets result:', {
      categoriesCount: Object.keys(facets.categories).length,
      categories: facets.categories,
      priceRangesCount: Object.keys(facets.priceRanges).length,
      provincesCount: Object.keys(facets.provinces).length
    });

    return facets
  }

  const handleFilterChange = (type: keyof ActiveFilters, value: string, checked: boolean) => {
    setTempFilters(prev => {
      const newFilters = { ...prev }
      if (!newFilters[type]) newFilters[type] = []

      const context = subcategoryContext || (window as any).subcategoryContext;
      const isKategoriType = type === 'kategori';

      if (checked) {
        if (!newFilters[type]!.includes(value)) {
          newFilters[type]!.push(value)
        }

        // ENHANCED: Handle both single category (with subcategories) and multi-category scenarios
        if (isKategoriType && context && context.allSubcategories) {
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);
          if (isSubcategory) {
            // Auto-check main category when subcategory is selected (single category mode)
            if (!newFilters[type]!.includes(context.category)) {
              newFilters[type]!.push(context.category);
            }
          }
        }
        // For multi-category mode, no special auto-check logic needed
      } else {
        // ENHANCED: Handle unchecking for both single and multi-category scenarios
        if (isKategoriType && context && context.allSubcategories) {
          // Single category mode with subcategories
          const isMainCategory = value === context.category;
          const hasSelectedSubcategories = context.allSubcategories.some((sub: any) =>
            newFilters[type]?.includes(sub.name)
          );

          // Don't allow unchecking main category if subcategories are selected
          if (isMainCategory && hasSelectedSubcategories) {
            return prev; // Return previous state without changes
          }

          // When unchecking subcategory, handle main category properly
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);
          if (isSubcategory) {
            // Remove the subcategory from filters
            const updatedFilters = newFilters[type]!.filter(item => item !== value);

            // Keep main category checked for proper filtering in single category mode
            if (!updatedFilters.includes(context.category)) {
              updatedFilters.push(context.category);
            }

            newFilters[type] = updatedFilters;

            // Apply filters immediately for desktop sidebar
            if (isDesktopSidebar) {
              setTimeout(() => {
                onFiltersChange(newFilters)
              }, 0)
            }

            return newFilters;
          }
        }

        // Regular unchecking for multi-category mode and other filter types
        newFilters[type] = newFilters[type]!.filter(item => item !== value)

        if (newFilters[type]!.length === 0) {
          delete newFilters[type]
        }
      }

      // FIXED: Auto-apply filters for desktop sidebar immediately
      if (isDesktopSidebar) {
        // Apply filters immediately for desktop sidebar
        setTimeout(() => {
          onFiltersChange(newFilters)
        }, 0)
      }

      return newFilters
    })
  }



  const applyFilters = () => {
    onFiltersChange(tempFilters)
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const resetFilters = () => {
    setTempFilters({})
    onFiltersChange({})
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const countActiveFilters = () => {
    return Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)
  }

  const toggleProvinceExpansion = (provinceName: string) => {
    setExpandedProvinces(prev => ({
      ...prev,
      [provinceName]: !prev[provinceName]
    }))
  }

  const getCitiesForProvince = (provinceName: string) => {
    // PERBAIKAN: Kumpulkan semua kota dari data asli, tapi hitung count dari data yang difilter
    const provinceCities: Record<string, number> = {}

    // Pertama, kumpulkan semua kota yang tersedia dari data asli
    const allCitiesInProvince = new Set<string>();
    if (searchResults) {
      searchResults.forEach((product: any) => {
        if (product.address?.province === provinceName && product.address?.city) {
          allCitiesInProvince.add(product.address.city);
        }
      });
    }

    // Inisialisasi semua kota dengan count 0
    allCitiesInProvince.forEach(cityName => {
      provinceCities[cityName] = 0;
    });

    // Kemudian hitung count berdasarkan produk yang difilter
    const productsToCheck = (displayedProducts && displayedProducts.length > 0) ? displayedProducts : (searchResults || [])

    console.log('🏙️ CITY COUNT: Calculating cities for province:', provinceName, {
      allCitiesAvailable: Array.from(allCitiesInProvince),
      productsToCheck: productsToCheck?.length || 0,
      displayedProducts: displayedProducts?.length || 0,
      searchResults: searchResults?.length || 0
    });

    if (productsToCheck) {
      productsToCheck.forEach((product: any) => {
        if (product.address?.province === provinceName && product.address?.city) {
          const cityName = product.address.city
          if (provinceCities.hasOwnProperty(cityName)) {
            provinceCities[cityName] = (provinceCities[cityName] || 0) + 1
            console.log(`🏙️ CITY COUNT: Found product "${product.name}" in ${cityName}, count now: ${provinceCities[cityName]}`);
          }
        }
      })
    }

    console.log('🏙️ CITY COUNT: Final cities for', provinceName, ':', provinceCities);
    return provinceCities
  }

  const renderFacetSection = (title: string, items: Record<string, number>, type: keyof ActiveFilters) => {
    console.log('🎯 FACET-RENDER: Rendering section:', title, 'items:', items, 'type:', type);

    // PERBAIKAN: Untuk kategori, tampilkan selama ada items (bahkan jika count 0)
    // Untuk section lain, hanya tampilkan jika ada items dengan count > 0
    const hasItems = title === 'Kategori'
      ? Object.keys(items).length > 0
      : Object.keys(items).some(key => items[key] > 0)

    console.log('🎯 FACET-RENDER: hasItems for', title, ':', hasItems);

    // Special debug for Kategori section
    if (title === 'Kategori') {
      console.log('🎯 KATEGORI SECTION DEBUG:', {
        title,
        itemsKeys: Object.keys(items),
        itemsCount: Object.keys(items).length,
        items: items,
        type: type,
        hasItems: hasItems
      });
    }
    if (!hasItems) {
      console.log('🎯 FACET-RENDER: Section', title, 'not rendered - no items');
      return null;
    }

    // Check if this is kategori section
    const context = subcategoryContext || (window as any).subcategoryContext;
    const isKategoriSection = type === 'kategori';

    // ENHANCED: Create effective context for hierarchical display
    let effectiveContext = context;
    if (!context && isKategoriSection && searchResults.length > 0) {
      const detectedCategory = detectMainCategoryFromResults(searchResults);
      if (detectedCategory) {
        effectiveContext = {
          category: detectedCategory.name,
          allSubcategories: detectedCategory.subcategories,
          selectedSubcategory: null
        };
      }
    }

    // ENHANCED: Show hierarchical structure for single category, flat structure for multi-category
    const shouldShowHierarchical = isKategoriSection && effectiveContext && effectiveContext.allSubcategories;

    // PERBAIKAN: Multi-kategori mode - deteksi berdasarkan kategori utama yang berbeda
    const mainCategories = Object.keys(items).filter(key => {
      // Kategori utama adalah yang tidak ada di daftar subkategori manapun
      const isMainCategory = ['Elektronik', 'Fashion Pria', 'Fashion Wanita', 'Kesehatan & Kecantikan'].includes(key);
      return isMainCategory;
    });

    const isMultiCategory = isKategoriSection && mainCategories.length > 1;

    console.log('🎯 FACET: Rendering categories:');
    console.log('  - isKategoriSection:', isKategoriSection);
    console.log('  - shouldShowHierarchical:', shouldShowHierarchical);
    console.log('  - isMultiCategory:', isMultiCategory);
    console.log('  - itemsCount:', Object.keys(items).length);
    console.log('  - items:', Object.keys(items));
    console.log('  - effectiveContext:', effectiveContext);
    console.log('  - tempFilters:', tempFilters);
    console.log('  - 🎯 MULTI-CATEGORY MODE ENABLED: Categories can be selected independently');

    return (
      <div className="facet-section">
        <h3>{title}</h3>
        <ul>
          {isMultiCategory ? (
            // Multi-category dropdown rendering - show only main categories with dropdown for subcategories
            (() => {
              console.log('🎯 MULTI-CATEGORY: Rendering dropdown structure');
              console.log('🎯 MULTI-CATEGORY: Main categories found:', mainCategories);
              console.log('🎯 MULTI-CATEGORY: All items:', items);

              // Definisi mapping kategori ke subkategori
              const categorySubcategoryMap: { [key: string]: string[] } = {
                'Elektronik': ['Audio & Wearables', 'Gaming & Console', 'Smartphone & Tablet', 'Komputer & Laptop', 'Kamera & Fotografi', 'TV & Audio', 'Elektronik Rumah', 'Aksesoris Elektronik', 'Perangkat Rumah Pintar', 'Elektronik Lainnya'],
                'Fashion Pria': ['Pakaian Pria', 'Sepatu Pria', 'Aksesoris Pria', 'Tas Pria', 'Jam Tangan Pria'],
                'Fashion Wanita': ['Pakaian Wanita', 'Sepatu Wanita', 'Aksesoris Wanita', 'Tas Wanita', 'Jam Tangan Wanita'],
                'Kesehatan & Kecantikan': ['Perawatan Wajah', 'Makeup', 'Perawatan Rambut', 'Perawatan Tubuh', 'Suplemen & Vitamin']
              };

              return mainCategories.map(mainCategory => {
                const mainCategoryCount = items[mainCategory] || 0;
                const isMainCategoryChecked = tempFilters[type]?.includes(mainCategory) || false;
                const mainCategoryCheckboxId = `facet-${type}-${mainCategory.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                // Dapatkan subkategori untuk kategori ini
                const subcategories = categorySubcategoryMap[mainCategory] || [];
                const subcategoriesWithCounts = subcategories.map(subcat => ({
                  name: subcat,
                  count: items[subcat] || 0
                }));

                // Check if this category dropdown is expanded
                const isExpanded = expandedProvinces[mainCategory] || false;
                const hasSubcategories = subcategoriesWithCounts.length > 0;

                return (
                  <li key={mainCategory} className="category-item-with-dropdown">
                    <div className="category-header">
                      <input
                        type="checkbox"
                        id={mainCategoryCheckboxId}
                        className="orange-checkbox"
                        checked={isMainCategoryChecked}
                        disabled={false} // PERBAIKAN: Jangan disable kategori dengan count 0
                        onChange={(e) => handleFilterChange(type, mainCategory, e.target.checked)}
                        data-facet-type={type}
                        data-facet-value={mainCategory}
                      />
                      <label htmlFor={mainCategoryCheckboxId} className={`category-name ${mainCategoryCount === 0 ? 'disabled' : ''}`}>
                        <strong>{mainCategory} ({mainCategoryCount})</strong>
                      </label>
                      {hasSubcategories && (
                        <button
                          className="subcategory-dropdown-toggle"
                          onClick={() => toggleProvinceExpansion(mainCategory)}
                          type="button"
                        >
                          {isExpanded ? '▲' : '▼'}
                        </button>
                      )}
                    </div>

                    {/* Subcategory dropdown */}
                    {isExpanded && hasSubcategories && (
                      <ul className="subcategory-dropdown">
                        {subcategoriesWithCounts.map(({ name: subcatName, count: subcatCount }) => {
                          const isSubcatChecked = tempFilters[type]?.includes(subcatName) || false;
                          const subcatCheckboxId = `facet-${type}-${subcatName.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                          return (
                            <li key={subcatName} className="subcategory-dropdown-item">
                              <input
                                type="checkbox"
                                id={subcatCheckboxId}
                                className="orange-checkbox"
                                checked={isSubcatChecked}
                                disabled={false} // PERBAIKAN: Jangan disable subkategori dengan count 0
                                onChange={(e) => handleFilterChange(type, subcatName, e.target.checked)}
                                data-facet-type={type}
                                data-facet-value={subcatName}
                              />
                              <label htmlFor={subcatCheckboxId} className={subcatCount === 0 ? 'disabled' : ''}>
                                {subcatName} ({subcatCount})
                              </label>
                            </li>
                          );
                        })}
                      </ul>
                    )}
                  </li>
                );
              });
            })()
          ) : shouldShowHierarchical ? (
            // Render category name first, then subcategories with indentation
            <>
              {/* Category name with checkbox - calculate total count */}
              {(() => {
                // Safety check for effective context
                if (!effectiveContext || !effectiveContext.category) {
                  return null;
                }

                const categoryCount = items[effectiveContext.category] || 0;
                // Check if any subcategory is selected
                const hasSelectedSubcategories = effectiveContext.allSubcategories?.some((sub: any) =>
                  tempFilters[type]?.includes(sub.name)
                ) || false;
                // ENHANCED: Auto-check main category in multiple scenarios
                // 1. If explicitly selected in filters
                // 2. If we're in subcategory context with selected subcategories
                // 3. If this is a regular search and this category is auto-selected
                const isInSubcategoryContext = context && context.allSubcategories;
                const isExplicitlyChecked = tempFilters[type]?.includes(effectiveContext.category);
                const hasSubcategoriesInContext = isInSubcategoryContext && hasSelectedSubcategories;
                const isCategoryChecked = isExplicitlyChecked || hasSubcategoriesInContext;
                const categoryCheckboxId = `facet-${type}-${effectiveContext.category.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                return (
                  <li>
                    <input
                      type="checkbox"
                      id={categoryCheckboxId}
                      className="orange-checkbox"
                      checked={isCategoryChecked}
                      disabled={true} // Always disable main category in search results
                      onChange={(e) => {
                        // Prevent any changes to main category in search results
                        e.preventDefault();
                      }}
                      data-facet-type={type}
                      data-facet-value={effectiveContext.category}
                    />
                    <label htmlFor={categoryCheckboxId} className={`category-name ${hasSelectedSubcategories ? 'disabled' : ''}`}>
                      {effectiveContext.category} ({categoryCount})
                    </label>
                  </li>
                );
              })()}

              {/* Subcategories with indentation and checkboxes */}
              {(() => {
                // Safety check for effective context
                if (!effectiveContext || !effectiveContext.category) {
                  return null;
                }

                // Get subcategories in sorted order (selected first)
                const subcategoryEntries = Object.entries(items).filter(([key]) => key !== effectiveContext.category);

                // Use sorted order if available, otherwise use original order
                let orderedSubcategories = subcategoryEntries;
                if (sortedSubcategories.length > 0) {
                  orderedSubcategories = sortedSubcategories
                    .map(name => subcategoryEntries.find(([key]) => key === name))
                    .filter(Boolean) as [string, number][];

                  // Add any missing subcategories at the end
                  const missingSubcategories = subcategoryEntries.filter(([key]) =>
                    !sortedSubcategories.includes(key)
                  );
                  orderedSubcategories = [...orderedSubcategories, ...missingSubcategories];
                }

                // SMART SORTING: Move disabled/zero count subcategories to bottom
                const enabledSubcategories = orderedSubcategories.filter(([_, count]) => count > 0);
                const disabledSubcategories = orderedSubcategories.filter(([_, count]) => count === 0);

                // Final order: enabled first, then disabled at bottom
                orderedSubcategories = [...enabledSubcategories, ...disabledSubcategories];

                // Show only 5 items initially with expand button for all devices
                const maxInitialItems = 5;
                const shouldShowExpandButton = orderedSubcategories.length > maxInitialItems;
                const itemsToShow = shouldShowExpandButton && !isSubcategoriesExpanded
                  ? orderedSubcategories.slice(0, maxInitialItems)
                  : orderedSubcategories;

                return (
                  <>
                    {itemsToShow.map(([key, count]) => {
                      const isChecked = tempFilters[type]?.includes(key) || false
                      const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                      // PERBAIKAN: Jangan disable filter options, biarkan user memilih meskipun count 0
                      // Ini memungkinkan user untuk memilih filter dan melihat "tidak ada produk"
                      // daripada menyembunyikan atau men-disable filter options
                      let isDisabled = false; // Selalu enable semua filter options

                      return (
                        <li key={key} className="subcategory-item">
                          <input
                            type="checkbox"
                            id={checkboxId}
                            className="orange-checkbox"
                            checked={isChecked}
                            onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                            data-facet-type={type}
                            data-facet-value={key}
                            disabled={isDisabled}
                          />
                          <label htmlFor={checkboxId} className={count === 0 ? 'text-gray-400' : ''}>
                            {key} ({count})
                          </label>
                        </li>
                      )
                    })}

                    {/* Expand/Collapse button for all devices */}
                    {shouldShowExpandButton && (
                      <li className="subcategory-expand-button">
                        <button
                          className="expand-button"
                          onClick={() => setIsSubcategoriesExpanded(!isSubcategoriesExpanded)}
                        >
                          {isSubcategoriesExpanded ? (
                            <>
                              <span>Tampilkan Lebih Sedikit</span>
                              <span className="expand-arrow up">▲</span>
                            </>
                          ) : (
                            <>
                              <span>Tampilkan {orderedSubcategories.length - maxInitialItems} Lainnya</span>
                              <span className="expand-arrow down">▼</span>
                            </>
                          )}
                        </button>
                      </li>
                    )}
                  </>
                );
              })()}
            </>
          ) : (
            // Regular rendering for other sections with smart sorting
            (() => {
              // SMART SORTING: Move disabled/zero count items to bottom for all sections
              const allEntries = Object.entries(items);
              const enabledEntries = allEntries.filter(([_, count]) => count > 0);
              const disabledEntries = allEntries.filter(([_, count]) => count === 0);
              const sortedEntries = [...enabledEntries, ...disabledEntries];

              // Special handling for provinces - show only 5 initially with expand button and city dropdown
              const isProvinceSection = type === 'provinsi';
              const maxInitialItems = 5;
              const shouldShowExpandButton = isProvinceSection && sortedEntries.length > maxInitialItems;
              const itemsToShow = shouldShowExpandButton && !isCitiesExpanded
                ? sortedEntries.slice(0, maxInitialItems)
                : sortedEntries;

              return (
                <>
                  {itemsToShow.map(([key, count]) => {
                    // Show all items regardless of count
                    const isChecked = tempFilters[type]?.includes(key) || false
                    const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                    // Special rendering for provinces with city dropdown
                    if (isProvinceSection) {
                      const isExpanded = expandedProvinces[key] || false
                      const cities = getCitiesForProvince(key)
                      const hasCities = Object.keys(cities).length > 0

                      return (
                        <li key={key} className="province-item">
                          <div className="province-header">
                            <input
                              type="checkbox"
                              id={checkboxId}
                              className="orange-checkbox"
                              checked={isChecked}
                              onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                              data-facet-type={type}
                              data-facet-value={key}
                              disabled={false} // PERBAIKAN: Jangan disable provinsi dengan count 0
                            />
                            <label htmlFor={checkboxId}>
                              {key} ({count})
                            </label>
                            {hasCities && (
                              <button
                                className="city-dropdown-toggle"
                                onClick={() => toggleProvinceExpansion(key)}
                                type="button"
                              >
                                {isExpanded ? '▲' : '▼'}
                              </button>
                            )}
                          </div>

                          {/* City dropdown */}
                          {isExpanded && hasCities && (
                            <ul className="city-dropdown">
                              {Object.entries(cities).map(([cityName, cityCount]) => {
                                const isCityChecked = tempFilters.kota?.includes(cityName) || false
                                const cityCheckboxId = `facet-kota-${cityName.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                                return (
                                  <li key={cityName} className="city-item">
                                    <input
                                      type="checkbox"
                                      id={cityCheckboxId}
                                      className="orange-checkbox"
                                      checked={isCityChecked}
                                      onChange={(e) => handleFilterChange('kota', cityName, e.target.checked)}
                                      data-facet-type="kota"
                                      data-facet-value={cityName}
                                      disabled={false} // PERBAIKAN: Jangan disable kota dengan count 0
                                    />
                                    <label htmlFor={cityCheckboxId}>
                                      {cityName} ({cityCount})
                                    </label>
                                  </li>
                                )
                              })}
                            </ul>
                          )}
                        </li>
                      )
                    }

                    // Regular rendering for non-province sections
                    return (
                      <li key={key}>
                        <input
                          type="checkbox"
                          id={checkboxId}
                          className="orange-checkbox"
                          checked={isChecked}
                          onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                          data-facet-type={type}
                          data-facet-value={key}
                          disabled={false} // PERBAIKAN: Jangan disable filter options dengan count 0
                        />
                        <label htmlFor={checkboxId}>
                          {key} ({count})
                        </label>
                      </li>
                    )
                  })}

                  {/* Expand/Collapse button for provinces */}
                  {shouldShowExpandButton && (
                    <li className="province-expand-button">
                      <button
                        className="expand-button"
                        onClick={() => setIsCitiesExpanded(!isCitiesExpanded)}
                      >
                        {isCitiesExpanded ? (
                          <>
                            <span>Tampilkan Lebih Sedikit</span>
                            <span className="expand-arrow up">▲</span>
                          </>
                        ) : (
                          <>
                            <span>Lihat {sortedEntries.length - maxInitialItems} Lainnya</span>
                            <span className="expand-arrow down">▼</span>
                          </>
                        )}
                      </button>
                    </li>
                  )}
                </>
              );
            })()
          )}
        </ul>
      </div>
    )
  }



  // For desktop sidebar, always show when isDesktopSidebar is true
  // For mobile/tablet popup, only show when isVisible is true
  if (!isDesktopSidebar && !isVisible) return null

  // Desktop Sidebar Layout
  if (isDesktopSidebar) {
    console.log('🎯 DESKTOP SIDEBAR: Rendering with facetData:', {
      categoriesCount: Object.keys(facetData.categories).length,
      categories: facetData.categories,
      provincesCount: Object.keys(facetData.provinces).length,
      priceRangesCount: Object.keys(facetData.priceRanges).length
    });

    return (
      <div className="facet-sidebar-desktop">
        <div className="facet-header">
          <div className="facet-title">
            <Filter size={18} className="facet-filter-icon" />
            Filter
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {(() => {
              console.log('🎯 RENDERING SECTIONS: facetData.categories:', facetData.categories);
              console.log('🎯 RENDERING SECTIONS: Object.keys(facetData.categories):', Object.keys(facetData.categories));
              return null;
            })()}
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Provinsi', facetData.provinces, 'provinsi')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    )
  }

  // Mobile/Tablet Popup Layout
  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <div className="facet-overlay" style={{ display: (isMobile || isTablet) ? 'flex' : 'none' }}>
        <div className="facet-panel">
          <div className="facet-header">
            <div className="facet-title">Filter</div>
            <div className="facet-close" onClick={onClose}>
              <X size={18} />
            </div>
          </div>

          <div className="facet-content-wrapper">
            <div className="facet-content">
              {renderFacetSection('Kategori', facetData.categories, 'kategori')}
              {renderFacetSection('Provinsi', facetData.provinces, 'provinsi')}
              {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
              {renderFacetSection('Rating', facetData.ratings, 'rating')}
              {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
              {renderFacetSection('Fitur', facetData.features, 'fitur')}
            </div>
          </div>

          <div className="facet-buttons">
            <div className="facet-button facet-button-reset" onClick={resetFilters}>
              Reset
            </div>
            <div className="facet-button facet-button-apply" onClick={applyFilters}>
              Terapkan ({countActiveFilters()})
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Panel - Only for non-sidebar mode */}
      <div
        className="facet-panel-desktop"
        style={{ display: (!isMobile && !isTablet) ? 'block' : 'none' }}
      >
        <div className="facet-header">
          <div className="facet-title">Filter</div>
          <div className="facet-close" onClick={onClose}>
            <X size={18} />
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Provinsi', facetData.provinces, 'provinsi')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    </>
  )
}