"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Area, AreaChart, Bar, BarChart, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { cn } from "@/lib/utils"

interface ChartCardProps {
  title: string
  description?: string
  data: any[]
  type?: "bar" | "line" | "area"
  dataKey: string
  className?: string
  height?: number
  showXAxis?: boolean
  showYAxis?: boolean
  showTooltip?: boolean
  valueFormatter?: (value: number) => string
  yAxisFormatter?: (value: number) => string
  colors?: {
    stroke?: string
    fill?: string
  }
}

export function ChartCard({
  title,
  description,
  data,
  type = "bar",
  dataKey,
  className,
  height = 300,
  showXAxis = true,
  showYAxis = true,
  showTooltip = true,
  valueFormatter = (value) => `${value}`,
  yAxisFormatter,
  colors = {
    stroke: type === "bar" ? "#3B82F6" : "hsl(var(--primary))",
    fill: type === "bar" ? "url(#blueGradient)" : "hsl(var(--primary) / 0.1)",
  },
}: ChartCardProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-4">
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-0 pb-6">
        <ResponsiveContainer width="100%" height={height}>
          {type === "bar" ? (
            <BarChart data={data} margin={{ top: 16, right: 24, bottom: 0, left: 8 }}>
              <defs>
                <linearGradient id="blueGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#3B82F6" />
                  <stop offset="100%" stopColor="#60A5FA" />
                </linearGradient>
              </defs>
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tickMargin={12}
                  fontSize={12}
                  stroke="hsl(var(--muted-foreground))"
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tickMargin={4}
                  fontSize={12}
                  stroke="hsl(var(--muted-foreground))"
                  tickFormatter={yAxisFormatter || valueFormatter}
                  dx={-15}
                />
              )}
              {showTooltip && (
                <Tooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="rounded-lg border border-border bg-background p-3 shadow-sm">
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex flex-col">
                              <span className="text-xs text-muted-foreground">{payload[0].payload.name}</span>
                              <span className="font-bold text-foreground">
                                {valueFormatter(payload[0].value as number)}
                              </span>
                            </div>
                          </div>
                        </div>
                      )
                    }
                    return null
                  }}
                />
              )}
              <Bar
                dataKey={dataKey}
                fill={colors.fill}
                stroke={colors.stroke}
                strokeWidth={1}
                radius={[4, 4, 0, 0]}
                barSize={20}
              />
            </BarChart>
          ) : type === "line" ? (
            <LineChart data={data} margin={{ top: 16, right: 24, bottom: 0, left: 8 }}>
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tickMargin={12}
                  fontSize={12}
                  stroke="hsl(var(--muted-foreground))"
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tickMargin={4}
                  fontSize={12}
                  stroke="hsl(var(--muted-foreground))"
                  tickFormatter={yAxisFormatter || valueFormatter}
                  dx={-15}
                />
              )}
              {showTooltip && (
                <Tooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="rounded-lg border border-border bg-background p-3 shadow-sm">
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex flex-col">
                              <span className="text-xs text-muted-foreground">{payload[0].payload.name}</span>
                              <span className="font-bold text-foreground">
                                {valueFormatter(payload[0].value as number)}
                              </span>
                            </div>
                          </div>
                        </div>
                      )
                    }
                    return null
                  }}
                />
              )}
              <Line
                type="monotone"
                dataKey={dataKey}
                stroke={colors.stroke}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6, strokeWidth: 0, fill: colors.stroke }}
                fill={colors.fill}
              />
            </LineChart>
          ) : type === "area" ? (
            <AreaChart data={data} margin={{ top: 16, right: 24, bottom: 0, left: -10 }}>
              <defs>
                <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="hsl(var(--primary))" stopOpacity={0.8} />
                  <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tickMargin={12}
                  fontSize={12}
                  stroke="hsl(var(--muted-foreground))"
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tickMargin={4}
                  fontSize={12}
                  stroke="hsl(var(--muted-foreground))"
                  tickFormatter={yAxisFormatter || valueFormatter}
                />
              )}
              {showTooltip && (
                <Tooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="rounded-lg border border-border bg-background p-3 shadow-sm">
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex flex-col">
                              <span className="text-xs text-muted-foreground">{payload[0].payload.name}</span>
                              <span className="font-bold text-foreground">
                                {valueFormatter(payload[0].value as number)}
                              </span>
                            </div>
                          </div>
                        </div>
                      )
                    }
                    return null
                  }}
                />
              )}
              <Area
                type="monotone"
                dataKey={dataKey}
                stroke="hsl(var(--primary))"
                strokeWidth={2}
                fill="url(#areaGradient)"
                dot={false}
                activeDot={{ r: 6, strokeWidth: 0, fill: "hsl(var(--primary))" }}
              />
            </AreaChart>
          ) : (
            <></>  
          )}
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
